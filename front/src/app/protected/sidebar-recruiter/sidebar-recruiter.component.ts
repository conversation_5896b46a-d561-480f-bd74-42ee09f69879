import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import {MatIcon} from "@angular/material/icon";
import {RouterLink, RouterLinkActive, Router} from "@angular/router";
import {Ng<PERSON><PERSON>, NgForOf, NgIf} from '@angular/common';
import { UserService } from '../../services/user/user.service';
import { WorkspaceService } from '../../services/workspace/workspace.service';
import { OidcSecurityService } from 'angular-auth-oidc-client';

@Component({
  selector: 'app-sidebar-recruiter',
  imports: [
    MatIcon,
    RouterLink,
    RouterLinkActive,
    NgClass,
    NgIf,
    NgForOf
  ],
  templateUrl: './sidebar-recruiter.component.html',
  standalone: true,
  styleUrl: './sidebar-recruiter.component.css'
})
export class SidebarRecruiterComponent implements OnInit {
  @Output() viewChange = new EventEmitter<string>();

  avatarUrl: string | null = null;
  userName: string = 'Recruteur';
  userEmail: string = '';
  collapsed: boolean = false;
  sidebarOpen: boolean = true;
  currentRoute: string = '';
  isLoading: boolean = true;
  currentView: string = 'dashboard';

  // Menu items with dynamic data
  menuItems = [
    {
      view: 'dashboard',
      icon: 'dashboard',
      label: 'Dashboard',
      active: true
    },
    {
      view: 'workspace',
      icon: 'business',
      label: 'Voir Workspace',
      active: false
    },
    {
      view: 'posts',
      icon: 'article',
      label: 'Vos Posts',
      active: false
    },
    {
      view: 'candidates',
      icon: 'people',
      label: 'Voir Candidats',
      active: false
    },
    {
      view: 'testResults',
      icon: 'assessment',
      label: 'Résultats de Test',
      active: false
    }
  ];

  constructor(
    private userService: UserService,
    private workspaceService: WorkspaceService,
    private oidcSecurityService: OidcSecurityService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadUserProfile();
    this.updateActiveRoute();

    // Écouter les changements de route
    this.router.events.subscribe(() => {
      this.updateActiveRoute();
    });
  }

  loadUserProfile() {
    this.isLoading = true;
    this.userService.getUserProfile().subscribe({
      next: (user) => {
        this.userName = user.firstName && user.lastName
          ? `${user.firstName} ${user.lastName}`
          : user.email || 'Recruteur';
        this.userEmail = user.email || '';
        this.avatarUrl = user.profilePicture || null;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement du profil:', error);
        this.userName = 'Recruteur';
        this.userEmail = '<EMAIL>';
        this.isLoading = false;
      }
    });
  }

  updateActiveRoute() {
    this.currentRoute = this.router.url;
    // Update active state based on current view instead of route
    this.menuItems.forEach(item => {
      item.active = item.view === this.currentView;
    });
  }

  logout() {
    this.oidcSecurityService.logoff().subscribe({
      next: () => {
        this.router.navigate(['/auth/signin']);
      },
      error: (error) => {
        console.error('Erreur lors de la déconnexion:', error);
        this.router.navigate(['/auth/signin']);
      }
    });
  }

  toggleSidebar() {
    this.collapsed = !this.collapsed;
  }

  navigateToProfile() {
    this.router.navigate(['/dashboard/profil']);
  }

  navigateToWorkspace() {
    this.setActiveView('workspace');
  }

  navigateToSettings() {
    this.router.navigate(['/settings']);
  }

  // View Management Methods
  setActiveView(view: string) {
    this.currentView = view;

    // Update menu items active state
    this.menuItems.forEach(item => {
      item.active = item.view === view;
    });

    // Emit view change to parent dashboard
    this.viewChange.emit(view);
  }

  onMenuItemClick(item: any) {
    this.setActiveView(item.view);
  }
}

