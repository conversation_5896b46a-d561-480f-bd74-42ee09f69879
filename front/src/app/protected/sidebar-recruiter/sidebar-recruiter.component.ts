import { Component, OnInit, Output, EventEmitter } from '@angular/core';
import {MatIcon} from "@angular/material/icon";
import {RouterLink, RouterLinkActive, Router} from "@angular/router";
import {Ng<PERSON><PERSON>, NgForOf, NgIf} from '@angular/common';
import { UserService } from '../../services/user/user.service';
import { WorkspaceService } from '../../services/workspace/workspace.service';
import { OidcSecurityService } from 'angular-auth-oidc-client';

@Component({
  selector: 'app-sidebar-recruiter',
  imports: [
    MatIcon,
    NgClass,
    NgIf,
    NgForOf
  ],
  templateUrl: './sidebar-recruiter.component.html',
  standalone: true,
  styleUrl: './sidebar-recruiter.component.css'
})
export class SidebarRecruiterComponent implements OnInit {
  @Output() viewChange = new EventEmitter<string>();

  avatarUrl: string | null = null;
  userName: string = 'Recruteur';
  userEmail: string = '';
  collapsed: boolean = false;
  sidebarOpen: boolean = true;
  currentRoute: string = '';
  isLoading: boolean = true;

  // Menu items with dynamic data
  menuItems = [
    {
      action: 'dashboard',
      icon: 'dashboard',
      label: 'Dashboard',
      active: true
    },
    {
      action: 'workspace',
      icon: 'business',
      label: 'Voir Workspace',
      active: false
    },
    {
      action: 'posts',
      icon: 'article',
      label: 'Vos Posts',
      active: false
    },
    {
      action: 'candidates',
      icon: 'people',
      label: 'Voir Candidats',
      active: false
    },
    {
      action: 'testResults',
      icon: 'assessment',
      label: 'Résultats de Test',
      active: false
    }
  ];

  constructor(
    private userService: UserService,
    private workspaceService: WorkspaceService,
    private oidcSecurityService: OidcSecurityService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadUserProfile();
    this.updateActiveRoute();

    // Écouter les changements de route
    this.router.events.subscribe(() => {
      this.updateActiveRoute();
    });
  }

  loadUserProfile() {
    this.isLoading = true;
    this.userService.getUserProfile().subscribe({
      next: (user) => {
        this.userName = user.firstName && user.lastName
          ? `${user.firstName} ${user.lastName}`
          : user.email || 'Recruteur';
        this.userEmail = user.email || '';
        this.avatarUrl = user.profilePicture || null;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Erreur lors du chargement du profil:', error);
        this.userName = 'Recruteur';
        this.userEmail = '<EMAIL>';
        this.isLoading = false;
      }
    });
  }

  updateActiveRoute() {
    this.currentRoute = this.router.url;
    // Les éléments de menu utilisent maintenant des actions au lieu de routes
    // L'état actif est géré par onMenuItemClick
  }

  logout() {
    this.oidcSecurityService.logoff().subscribe({
      next: () => {
        this.router.navigate(['/auth/signin']);
      },
      error: (error) => {
        console.error('Erreur lors de la déconnexion:', error);
        this.router.navigate(['/auth/signin']);
      }
    });
  }

  toggleSidebar() {
    this.collapsed = !this.collapsed;
  }

  navigateToProfile() {
    this.router.navigate(['/dashboard/profil']);
  }

  navigateToWorkspace() {
    // Naviguer vers la page de profil du workspace actif
    this.router.navigate(['/workspace-profile']);
  }

  navigateToSettings() {
    // Naviguer vers la page de paramètres
    this.router.navigate(['/settings']);
  }

  // Nouvelle méthode pour gérer les clics sur les éléments de menu
  onMenuItemClick(action: string) {
    // Mettre à jour l'état actif
    this.menuItems.forEach(item => {
      item.active = item.action === action;
    });

    // Émettre l'événement vers le dashboard
    this.viewChange.emit(action);
  }
}

