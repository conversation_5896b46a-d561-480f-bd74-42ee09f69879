/* ADNIA Style - Bleu Marine & Orange */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --adnia-navy: #2B3A67;
  --adnia-navy-dark: #1E2A4A;
  --adnia-navy-light: #3A4B7A;
  --adnia-orange: #FF6B35;
  --adnia-orange-light: #FF8A65;
  --adnia-teal: #26A69A;
  --adnia-white: #FFFFFF;
  --adnia-gray-light: #F5F7FA;
  --adnia-gray: #8E9AAF;
  --adnia-text-light: rgba(255, 255, 255, 0.95);
  --adnia-text-muted: rgba(255, 255, 255, 0.7);
  --adnia-border: rgba(255, 255, 255, 0.08);
  --adnia-shadow: 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.sidebar {
  width: 200px;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background: var(--adnia-dark);
  font-family: 'Inter', sans-serif;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: var(--adnia-shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.sidebar.collapsed {
  width: 70px;
}

/* Toggle Button - Hidden for ADNIA style */
.toggle-btn {
  display: none;
}

/* Header Brand - ADNIA Style */
.user-profile {
  display: none;
}

.profile-avatar {
  position: relative;
  margin-right: 12px;
}

.avatar,
.avatar-placeholder {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.avatar {
  object-fit: cover;
  border: 3px solid var(--accent-gold);
  box-shadow: 0 8px 25px rgba(245, 158, 11, 0.2);
}

.avatar-placeholder {
  background: var(--gradient-executive);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid var(--accent-gold);
}

.avatar-icon {
  font-size: 24px;
  color: var(--white);
}

.avatar-skeleton {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--border-light);
  animation: pulse 1.5s infinite;
}

.status-indicator {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 10px;
  height: 10px;
  background: #22c55e;
  border: 2px solid var(--white);
  border-radius: 50%;
}

.profile-info {
  flex: 1;
  transition: all 0.3s ease;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
  letter-spacing: -0.2px;
}

.user-email {
  font-size: 13px;
  color: var(--text-muted);
  font-weight: 400;
}

.skeleton {
  background: var(--border-light);
  border-radius: 4px;
  height: 14px;
  width: 100px;
  animation: pulse 1.5s infinite;
}

/* ADNIA Header */
.sidebar-header {
  padding: 25px 20px;
  border-bottom: 1px solid var(--adnia-border);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-icon {
  font-size: 24px;
  color: var(--adnia-text);
  margin-right: 8px;
  font-weight: 700;
}

.dashboard-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--adnia-text);
  margin: 0;
  letter-spacing: 0.5px;
}

.dashboard-subtitle {
  display: none;
}

/* ADNIA Navigation */
.sidebar-nav {
  flex: 1;
  padding: 15px 0;
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu li {
  margin: 2px 0;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: var(--adnia-text-muted);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
}

.menu-link:hover {
  background: var(--adnia-darker);
  color: var(--adnia-text);
}

.sidebar-menu li.active .menu-link {
  background: var(--adnia-darker);
  color: var(--adnia-text);
  font-weight: 600;
  border-left: 3px solid var(--adnia-accent);
}

.menu-icon-container {
  margin-right: 12px;
  width: 16px;
  display: flex;
  justify-content: center;
}

.menu-icon {
  font-size: 16px;
  transition: all 0.2s ease;
}

.sidebar-menu li.active .menu-icon {
  color: var(--adnia-accent);
}

.menu-label {
  flex: 1;
  font-size: 14px;
}

/* ADNIA Footer */
.sidebar-footer {
  padding: 15px 20px;
  border-top: 1px solid var(--adnia-border);
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.settings-btn,
.logout-btn {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  padding: 12px 0;
  background: transparent;
  border: none;
  color: var(--adnia-text-muted);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.settings-btn:hover,
.logout-btn:hover {
  color: var(--adnia-text);
}

.settings-icon-container,
.logout-icon-container {
  margin-right: 12px;
  width: 16px;
  display: flex;
  justify-content: center;
}

.settings-icon,
.logout-icon {
  font-size: 16px;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    width: 70px;
  }

  .profile-info,
  .sidebar-header,
  .menu-label,
  .logout-btn span {
    display: none;
  }

  .user-profile,
  .menu-link,
  .logout-btn {
    justify-content: center;
  }

  .menu-icon-container {
    margin: 0;
  }

  .logout-icon-container {
    margin: 0;
  }
}

.sidebar.collapsed .profile-info,
.sidebar.collapsed .sidebar-header,
.sidebar.collapsed .menu-label,
.sidebar.collapsed .logout-btn span {
  display: none;
}

.sidebar.collapsed .user-profile,
.sidebar.collapsed .menu-link,
.sidebar.collapsed .logout-btn {
  justify-content: center;
}

.sidebar.collapsed .menu-icon-container {
  margin: 0;
}

.sidebar.collapsed .logout-icon-container {
  margin: 0;
}
