<!-- Section Header (style dashboard) -->
<div class="section-header">
  <h2>Résultats des Tests</h2>
  <p>Analysez les performances des candidats et gérez les acceptations</p>
</div>

<!-- Filtres et Actions -->
<div class="filters-section">
  <button class="btn btn-secondary" (click)="toggleFilters()">
    <mat-icon>filter_list</mat-icon>
    {{ showFilters ? 'Masquer les filtres' : 'Afficher les filtres' }}
  </button>
  <button class="btn btn-primary" (click)="loadTestResults()">
    <mat-icon>refresh</mat-icon>
    Actualiser
  </button>
</div>

<!-- Panneau de filtres -->
<div class="filters-panel" *ngIf="showFilters">
  <div class="filter-row">
    <div class="filter-group">
      <label>Score :</label>
      <select [(ngModel)]="filters.scoreRange" (change)="onFilterChange()" class="filter-select">
        <option *ngFor="let option of scoreRangeOptions" [value]="option.value">{{ option.label }}</option>
      </select>
    </div>
    <div class="filter-group">
      <label>Compétence :</label>
      <select [(ngModel)]="filters.competence" (change)="onFilterChange()" class="filter-select">
        <option *ngFor="let competence of competenceOptions" [value]="competence">{{ competence }}</option>
      </select>
    </div>
    <div class="filter-group">
      <label>Trier par :</label>
      <select [(ngModel)]="filters.sortBy" (change)="onFilterChange()" class="filter-select">
        <option *ngFor="let option of sortOptions" [value]="option.value">{{ option.label }}</option>
      </select>
    </div>
    <button class="btn btn-outline" (click)="resetFilters()">
      <mat-icon>clear</mat-icon>
      Réinitialiser
    </button>
  </div>
</div>

<!-- Indicateur de chargement -->
<div *ngIf="loading" class="loading-state">
  <mat-icon>hourglass_empty</mat-icon>
  <h3>Chargement en cours...</h3>
  <p>Récupération des résultats de test depuis la base de données</p>
</div>

<!-- Message d'erreur -->
<div *ngIf="error && !loading" class="error-state">
  <mat-icon>error_outline</mat-icon>
  <h3>Erreur de chargement</h3>
  <p>{{ error }}</p>
  <button class="btn btn-primary" (click)="loadTestResults()">
    <mat-icon>refresh</mat-icon>
    Réessayer
  </button>
</div>

<!-- Liste des résultats (style dashboard) -->
<div *ngIf="!loading && !error" class="list-container">
  <!-- Message si aucun résultat -->
  <div *ngIf="paginatedResults.length === 0" class="empty-state">
    <mat-icon>quiz</mat-icon>
    <h3>Aucun résultat de test</h3>
    <p>Aucun test n'a été complété pour les offres de ce workspace.</p>
    <button class="btn btn-primary" (click)="loadTestResults()">
      <mat-icon>refresh</mat-icon>
      Actualiser
    </button>
  </div>

  <!-- Liste des résultats (style dashboard) -->
  <div class="list-item" *ngFor="let result of paginatedResults; trackBy: trackByResultId">
    <div class="result-header">
      <h3 (click)="viewCandidateProfile(result)" class="candidate-name">{{ result.name }}</h3>
      <div class="result-meta">
        <span class="test-name">{{ result.job }}</span>
        <span class="test-date">{{ result.date }}</span>
      </div>
    </div>

    <div class="result-details">
      <div class="score-info">
        <span class="score-label">Score:</span>
        <span class="score-value" [class]="getScoreClass(parseInt(result.score))">{{ result.score }}</span>
      </div>

      <div class="time-info">
        <span><mat-icon>schedule</mat-icon> Limite: {{ result.timeLimit }}</span>
        <span><mat-icon>timer</mat-icon> Durée: {{ result.timePassed }}</span>
      </div>

      <div class="answers-info" *ngIf="result.correctAnswers && result.totalQuestions">
        <span>{{ result.correctAnswers }}/{{ result.totalQuestions }} bonnes réponses</span>
      </div>
    </div>

    <div class="result-actions">
      <button class="btn btn-sm" (click)="viewTestDetails(result)" title="Voir les détails">
        <mat-icon>visibility</mat-icon>
        Détails
      </button>

      <button class="btn btn-sm" (click)="viewCandidateProfile(result)" title="Voir le profil">
        <mat-icon>account_circle</mat-icon>
        Profil
      </button>

      <!-- Boutons d'acceptation/refus -->
      <button class="btn btn-accent" (click)="acceptCandidate(result)"
              *ngIf="shouldShowActionButtons(result)" title="Accepter le candidat">
        <mat-icon>check_circle</mat-icon>
        Accepter
      </button>

      <button class="btn btn-danger" (click)="rejectCandidate(result)"
              *ngIf="shouldShowActionButtons(result)" title="Refuser le candidat">
        <mat-icon>cancel</mat-icon>
        Refuser
      </button>
    </div>

    <!-- Badge de statut -->
    <span class="status-badge" [class]="getStatusClass(result.status)">
      {{ getStatusText(result.status) }}
    </span>
  </div>
</div>

<!-- Pagination (style dashboard) -->
<div class="pagination" *ngIf="totalPages > 1">
  <button class="pagination-btn" (click)="prevPage()" [disabled]="currentPage === 1">
    <mat-icon>chevron_left</mat-icon>
    Précédent
  </button>

  <div class="pagination-pages">
    <span *ngFor="let page of getVisiblePages()"
          (click)="goToPage(page)"
          [class.active]="currentPage === page"
          class="pagination-page">
      {{ page }}
    </span>
  </div>

  <button class="pagination-btn" (click)="nextPage()" [disabled]="currentPage === totalPages">
    Suivant
    <mat-icon>chevron_right</mat-icon>
  </button>
</div>

<!-- Informations de pagination -->
<div class="pagination-info" *ngIf="filteredResults.length > 0">
  <p>Affichage de {{ (currentPage - 1) * itemsPerPage + 1 }} à {{ Math.min(currentPage * itemsPerPage, filteredResults.length) }} sur {{ filteredResults.length }} résultats</p>
</div>
