<div class="results-page">

  <div class="results-container">
    <header class="results-header">
      <h1>Test Results</h1>
      <button mat-button class="filter-button">
        <mat-icon>filter_list</mat-icon> Filtrer par
      </button>
    </header>

    <!-- Indicateur de chargement -->
    <div *ngIf="loading" class="loading-container">
      <mat-progress-spinner mode="indeterminate" diameter="50"></mat-progress-spinner>
      <p>Chargement des résultats depuis la base de données...</p>
    </div>

    <!-- Message d'erreur -->
    <div *ngIf="error && !loading" class="error-container">
      <mat-icon color="warn">error</mat-icon>
      <p>{{ error }}</p>
      <button mat-button color="primary" (click)="refreshResults()">
        <mat-icon>refresh</mat-icon>
        Réessayer
      </button>
    </div>

    <!-- Grille des résultats -->
    <div *ngIf="!loading && !error" class="results-grid">
      <!-- Message si aucun résultat -->
      <div *ngIf="paginatedResults.length === 0" class="no-results">
        <mat-icon>search_off</mat-icon>
        <h3>Aucun résultat trouvé</h3>
        <p>Aucun résultat de test trouvé dans la base de données</p>
        <button mat-button color="primary" (click)="refreshResults()">
          <mat-icon>refresh</mat-icon>
          Actualiser
        </button>
      </div>

      <!-- Cartes de résultats -->
      <mat-card class="result-card" *ngFor="let result of paginatedResults; trackBy: trackByResultId">
        <mat-card-header class="result-header">
          <mat-card-title>Job: {{ result.job }}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="candidate-info">
            <!-- Icône de personne -->
            <mat-icon class="avatar">account_circle</mat-icon>
            <div>
              <p>Nom: {{ result.name }}</p>
              <p><mat-icon>event</mat-icon> {{ result.date }}</p>
            </div>
          </div>

          <div class="test-info">
            <div class="time-limit">
              <p><mat-icon>schedule</mat-icon> Time Limit: <span class="red-text">{{ result.timeLimit }}</span></p>
            </div>
            <p><mat-icon>timer</mat-icon> Time Passed: {{ result.timePassed }}</p>
          </div>

          <div class="score">
            <strong>Score:</strong> {{ result.score }}
          </div>

          <!-- Statut du candidat -->
          <div class="candidate-status">
            <span class="status-badge" [ngClass]="getStatusClass(result.status)">
              {{ getStatusText(result.status) }}
            </span>
          </div>
        </mat-card-content>

        <!-- Actions principales -->
        <div class="result-actions">
          <button mat-icon-button color="primary" (click)="viewTestDetails(result)" title="Voir les détails">
            <mat-icon>visibility</mat-icon>
          </button>
          <button mat-icon-button color="accent" (click)="viewCandidateProfile(result)" title="Voir le profil">
            <mat-icon>account_circle</mat-icon>
          </button>
          <button mat-icon-button color="warn" (click)="deleteTestResult(result)" title="Supprimer">
            <mat-icon>delete</mat-icon>
          </button>
        </div>

        <!-- Boutons d'acceptation/refus -->
        <div class="candidate-actions" *ngIf="shouldShowActionButtons(result)">
          <button mat-raised-button color="primary" class="accept-btn" (click)="acceptCandidate(result)">
            <mat-icon>check_circle</mat-icon>
            Accepter
          </button>
          <button mat-raised-button color="warn" class="reject-btn" (click)="rejectCandidate(result)">
            <mat-icon>cancel</mat-icon>
            Refuser
          </button>
        </div>

        <!-- Message pour candidats déjà traités -->
        <div class="candidate-processed" *ngIf="!shouldShowActionButtons(result)">
          <mat-icon *ngIf="result.status === 'accepted'" [ngClass]="getStatusClass(result.status)">check_circle</mat-icon>
          <mat-icon *ngIf="result.status !== 'accepted'" [ngClass]="getStatusClass(result.status)">cancel</mat-icon>
          <span>Candidat {{ getStatusText(result.status).toLowerCase() }}</span>
        </div>
      </mat-card>
    </div>

    <!-- Pagination -->
    <footer class="pagination-footer">
      <!-- Boutons de pagination -->
      <button mat-button class="pagination-button" (click)="prevPage()" [disabled]="currentPage === 1">
        <mat-icon>chevron_left</mat-icon>
      </button>

      <!-- Pages dynamiques -->
      <span *ngFor="let page of pages"
            (click)="goToPage(page)"
            [class.active]="currentPage === page"
            class="pagination-page">
    {{ page }}
  </span>

      <!-- Bouton suivant -->
      <button mat-button class="pagination-button" (click)="nextPage()" [disabled]="currentPage === totalPages">
        <mat-icon>chevron_right</mat-icon>
      </button>
    </footer>

  </div>
</div>
