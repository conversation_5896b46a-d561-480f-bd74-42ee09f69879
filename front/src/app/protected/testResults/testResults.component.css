/* ==========================================================================
   STYLES DASHBOARD - PAGE TEST RESULTS
   Style moderne, élégant et professionnel
   ========================================================================== */

/* Section Header */
.section-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #f1f5f9;
}

.section-header h2 {
  font-size: 1.875rem;
  font-weight: 700;
  color: #001040FF;
  margin-bottom: 0.5rem;
  letter-spacing: -0.025em;
}

.section-header p {
  color: #64748b;
  font-size: 1rem;
  font-weight: 400;
}

/* Filtres Section */
.filters-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: center;
  flex-wrap: wrap;
}
/* Boutons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  border: none;
  text-decoration: none;
  white-space: nowrap;
}

.btn-primary {
  background: linear-gradient(135deg, #001040FF 0%, #001660FF 100%);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(0, 16, 64, 0.1);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #001660FF 0%, #002080FF 100%);
  box-shadow: 0 10px 15px -3px rgba(0, 16, 64, 0.2);
  transform: translateY(-1px);
}

.btn-secondary {
  background: #f8fafc;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.btn-accent {
  background: linear-gradient(135deg, #ff6600 0%, #ff8533 100%);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(255, 102, 0, 0.1);
}

.btn-accent:hover {
  background: linear-gradient(135deg, #ff8533 0%, #ff9966 100%);
  box-shadow: 0 10px 15px -3px rgba(255, 102, 0, 0.2);
  transform: translateY(-1px);
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.1);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: 0 10px 15px -3px rgba(239, 68, 68, 0.2);
  transform: translateY(-1px);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.btn-outline {
  background: transparent;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-outline:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
}

/* Panneau de filtres */
.filters-panel {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.filter-row {
  display: flex;
  gap: 1.5rem;
  align-items: end;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  min-width: 150px;
}

.filter-group label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.filter-select {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;
}

.filter-select:focus {
  outline: none;
  border-color: #001040FF;
  box-shadow: 0 0 0 3px rgba(0, 16, 64, 0.1);
}

/* États de chargement et d'erreur */
.loading-state, .error-state, .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  background: #f8fafc;
  border-radius: 1rem;
  border: 2px dashed #e2e8f0;
  margin: 2rem 0;
}

.loading-state mat-icon, .error-state mat-icon, .empty-state mat-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
  color: #64748b;
  margin-bottom: 1rem;
}

.loading-state h3, .error-state h3, .empty-state h3 {
  color: #1e293b;
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.loading-state p, .error-state p, .empty-state p {
  color: #64748b;
  font-size: 1rem;
  margin-bottom: 1rem;
}

/* Liste des résultats */
.list-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.list-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.2s ease-in-out;
  position: relative;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.list-item:hover {
  border-color: #001040FF;
  box-shadow: 0 4px 6px -1px rgba(0, 16, 64, 0.1);
  transform: translateY(-1px);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.candidate-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #001040FF;
  cursor: pointer;
  margin: 0;
  transition: color 0.2s ease-in-out;
}

.candidate-name:hover {
  color: #ff6600;
  text-decoration: underline;
}

.result-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.test-name {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.test-date {
  font-size: 0.75rem;
  color: #64748b;
}

.candidate-processed {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  margin-top: 16px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.candidate-processed mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.candidate-processed .status-accepted {
  color: #22C55E;
}

.candidate-processed .status-rejected {
  color: #EF4444;
}

/* ==========================================================================
   Statut et actions des candidats
   ========================================================================== */
.candidate-status {
  margin: 16px 0;
  display: flex;
  justify-content: center;
}

.status-badge {
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-pending {
  background: rgba(251, 191, 36, 0.2);
  color: #F59E0B;
  border: 1px solid rgba(251, 191, 36, 0.3);
}

.status-accepted {
  background: rgba(34, 197, 94, 0.2);
  color: #22C55E;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-rejected {
  background: rgba(239, 68, 68, 0.2);
  color: #EF4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.candidate-actions {
  display: flex;
  gap: 12px;
  padding: 16px;
  justify-content: center;
  background: rgba(255, 255, 255, 0.02);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.accept-btn {
  background: linear-gradient(135deg, #22C55E, #16A34A) !important;
  color: white !important;
  border: none;
  border-radius: 12px;
  padding: 8px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(34, 197, 94, 0.3);
}

.accept-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
  background: linear-gradient(135deg, #16A34A, #15803D) !important;
}

.reject-btn {
  background: linear-gradient(135deg, #EF4444, #DC2626) !important;
  color: white !important;
  border: none;
  border-radius: 12px;
  padding: 8px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.reject-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
  background: linear-gradient(135deg, #DC2626, #B91C1C) !important;
}

/* Détails des résultats */
.result-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
}

.score-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.score-label {
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.score-value {
  font-weight: 700;
  font-size: 1.125rem;
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
}

.score-value.excellent {
  background: #dcfce7;
  color: #166534;
}

.score-value.good {
  background: #dbeafe;
  color: #1d4ed8;
}

.score-value.average {
  background: #fef3c7;
  color: #92400e;
}

.score-value.poor {
  background: #fee2e2;
  color: #dc2626;
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.time-info span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  color: #64748b;
}

.time-info mat-icon {
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
}

.answers-info {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #374151;
  font-weight: 500;
}

/* Actions des résultats */
.result-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

/* Badges de statut */
.status-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-badge.status-accepted {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.status-badge.status-rejected {
  background: #fee2e2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.status-badge.status-pending {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
  margin-top: 2rem;
  padding: 1rem;
}

.pagination-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.pagination-btn:hover:not(:disabled) {
  background: #f8fafc;
  border-color: #001040FF;
  color: #001040FF;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  gap: 0.25rem;
}

.pagination-page {
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  color: #64748b;
}

.pagination-page:hover {
  background: #f1f5f9;
  color: #001040FF;
}

.pagination-page.active {
  background: #001040FF;
  color: white;
}

.pagination-info {
  text-align: center;
  margin-top: 1rem;
  color: #64748b;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .section-header h2 {
    font-size: 1.5rem;
  }

  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-row {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-group {
    min-width: auto;
  }

  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .result-meta {
    align-items: flex-start;
  }

  .result-details {
    grid-template-columns: 1fr;
  }

  .result-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .pagination {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .pagination-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .list-item {
    padding: 1rem;
  }

  .status-badge {
    position: static;
    margin-top: 0.5rem;
    align-self: flex-start;
  }

  .pagination-pages {
    display: none;
  }
}

/* ==========================================================================
   Grille des résultats
   ========================================================================== */
.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

/* ==========================================================================
   Carte de résultat
   ========================================================================== */
.result-card {
  background-color: #FFFFFF; /* Fond blanc pour les cartes */
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* Ombre douce */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.result-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1); /* Ombre renforcée au survol */
}

/* ==========================================================================
   En-tête de la carte
   ========================================================================== */
.result-header {
  margin-bottom: 12px;
}

.result-header mat-card-title {
  font-size: 16px;
  font-weight: 500;
  color: #1C2526; /* Texte sombre pour les titres */
}

/* ==========================================================================
   Informations du candidat
   ========================================================================== */
.candidate-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.candidate-info .avatar {
  font-size: 36px;
  width: 36px;
  height: 36px;
  color: #FFFFFF; /* Icône blanche */
  background-color: #0A66C2; /* Bleu professionnel pour avatar */
  border-radius: 50%;
  padding: 8px;
  margin-right: 12px;
}

.candidate-info p {
  font-size: 14px;
  color: #1C2526; /* Texte sombre */
  margin: 4px 0;
}

.candidate-info mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  vertical-align: middle;
  color: #6B7280; /* Gris moyen pour icônes */
}

/* ==========================================================================
   Informations du test
   ========================================================================== */
.test-info {
  margin-bottom: 16px;
}

.test-info p {
  font-size: 14px;
  color: #1C2526; /* Texte sombre */
  display: flex;
  align-items: center;
  margin: 8px 0;
}

.test-info mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #0A66C2; /* Bleu pour icônes */
}

.test-info .time-limit .red-text {
  color: #EF4444; /* Rouge pour alerte */
}

/* ==========================================================================
   Score
   ========================================================================== */
.score {
  font-size: 15px;
  color: #1C2526; /* Texte sombre */
  font-weight: 500;
}

/* ==========================================================================
   Actions de la carte
   ========================================================================== */
.result-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.result-actions mat-icon-button {
  color: #6B7280; /* Gris moyen par défaut */
}

.result-actions mat-icon-button[color="primary"]:hover {
  color: #0A66C2; /* Bleu au survol */
}

.result-actions mat-icon-button[color="warn"]:hover {
  color: #EF4444; /* Rouge au survol */
}

.result-actions mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* ==========================================================================
   Pagination
   ========================================================================== */
.pagination-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 16px 0;
}

.pagination-footer .pagination-button {
  background-color: #F3F2EF; /* Gris clair pour boutons */
  color: #1C2526; /* Texte sombre */
  border-radius: 6px;
  padding: 8px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.pagination-footer .pagination-button:hover:not([disabled]) {
  background-color: #E5E7EB; /* Gris légèrement plus foncé au survol */
  color: #0A66C2; /* Bleu pour interaction */
}

.pagination-footer .pagination-button[disabled] {
  background-color: #E5E7EB; /* Gris clair désactivé */
  color: #6B7280; /* Texte gris moyen */
  cursor: not-allowed;
}

.pagination-footer .pagination-button mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: #1C2526; /* Icône sombre */
}

.pagination-footer .pagination-page {
  font-size: 14px;
  color: #1C2526; /* Texte sombre */
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.pagination-footer .pagination-page:hover {
  background-color: #E5E7EB; /* Gris clair au survol */
  color: #0A66C2; /* Bleu pour interaction */
}

.pagination-footer .pagination-page.active {
  background-color: #0A66C2; /* Bleu pour page active */
  color: #FFFFFF; /* Blanc pour contraste */
  font-weight: 500;
}

/* ==========================================================================
   Responsive
   ========================================================================== */
@media (max-width: 768px) {
  .results-container {
    margin-left: 70px;
    width: calc(100% - 70px);
    padding: 16px;
  }

  .results-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .results-grid {
    grid-template-columns: 1fr;
  }

  .results-header .filter-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 600px) {
  .results-container {
    padding: 12px;
  }

  .results-header h1 {
    font-size: 22px;
  }

  .pagination-footer .pagination-page {
    padding: 6px 10px;
    font-size: 12px;
  }
}
