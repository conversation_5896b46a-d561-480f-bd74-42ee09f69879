@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

:root {
  /* Professional Color Palette */
  --primary-navy: #001040FF;
  --secondary-navy: #001660FF;
  --accent-orange: #FF6B35;
  --accent-orange-light: #FF8A65;
  --accent-orange-dark: #E55722;

  /* Background & Surface Colors */
  --background-primary: #F8FAFC;
  --background-secondary: #F1F5F9;
  --surface-primary: #FFFFFF;
  --surface-secondary: rgba(255, 255, 255, 0.95);
  --surface-glass: rgba(255, 255, 255, 0.1);

  /* Text Colors */
  --text-primary: #1E293B;
  --text-secondary: #64748B;
  --text-muted: #94A3B8;
  --text-white: #FFFFFF;

  /* Border & Divider Colors */
  --border-light: #E2E8F0;
  --border-medium: #CBD5E1;
  --divider: rgba(0, 16, 64, 0.1);

  /* Shadow System */
  --shadow-xs: 0 1px 2px rgba(0, 16, 64, 0.05);
  --shadow-sm: 0 2px 8px rgba(0, 16, 64, 0.08);
  --shadow-md: 0 8px 25px rgba(0, 16, 64, 0.12);
  --shadow-lg: 0 16px 40px rgba(0, 16, 64, 0.15);
  --shadow-xl: 0 25px 50px rgba(0, 16, 64, 0.20);

  /* Glass Morphism */
  --glass-bg: rgba(255, 255, 255, 0.25);
  --glass-border: rgba(255, 255, 255, 0.18);
  --glass-shadow: 0 8px 32px rgba(0, 16, 64, 0.1);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-navy) 0%, var(--secondary-navy) 100%);
  --gradient-orange: linear-gradient(135deg, var(--accent-orange) 0%, var(--accent-orange-dark) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);

  /* Transitions */
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-smooth: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  --transition-slow: all 0.5s cubic-bezier(0.25, 0.8, 0.25, 1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: var(--background-primary);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dashboard {
  display: grid;
  grid-template-columns: 200px 1fr;
  grid-template-areas: "sidebar main";
  min-height: 100vh;
  background: var(--background-primary);
  position: relative;
}

.dashboard::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(0, 16, 64, 0.02) 0%,
    rgba(255, 107, 53, 0.01) 50%,
    rgba(0, 22, 96, 0.02) 100%);
  pointer-events: none;
  z-index: 0;
}

/* Sidebar */
app-sidebar-recruiter {
  grid-area: sidebar;
  position: relative;
  z-index: 10;
}

/* Main Content */
.main-content {
  grid-area: main;
  padding: 0;
  background: transparent;
  position: relative;
  z-index: 1;
  overflow-x: hidden;
}

/* Professional Header */
.dashboard-header {
  background: var(--surface-primary);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  margin: 24px;
  border-radius: 20px;
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.dashboard-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-orange);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px;
  position: relative;
  z-index: 1;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dashboard-title {
  font-size: 32px;
  font-weight: 700;
  color: var(--primary-navy);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  letter-spacing: -0.5px;
}

.title-icon {
  font-size: 36px;
  color: var(--accent-orange);
  background: linear-gradient(135deg, var(--accent-orange), var(--accent-orange-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dashboard-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-secondary);
  margin: 0;
  opacity: 0.8;
}

/* Search Container */
.search-container {
  display: flex;
  align-items: center;
  background: var(--surface-secondary);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  padding: 12px 20px;
  transition: var(--transition-smooth);
  width: 320px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.search-container:focus-within {
  border-color: var(--accent-orange);
  box-shadow: 0 0 0 4px rgba(255, 107, 53, 0.1);
  background: var(--surface-primary);
}

.search-icon {
  color: var(--text-muted);
  font-size: 20px;
  margin-right: 12px;
  transition: var(--transition-fast);
}

.search-container:focus-within .search-icon {
  color: var(--accent-orange);
}

.search-bar {
  border: none;
  background: transparent;
  flex: 1;
  font-size: 15px;
  font-weight: 500;
  color: var(--text-primary);
  outline: none;
  font-family: inherit;
}

.search-bar::placeholder {
  color: var(--text-muted);
  font-weight: 400;
}

/* Control Buttons */
.notification-button,
.refresh-button,
.export-button {
  width: 48px;
  height: 48px;
  background: var(--surface-secondary);
  border: 1px solid var(--border-light);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  transition: var(--transition-smooth);
  cursor: pointer;
  position: relative;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.notification-button:hover,
.refresh-button:hover,
.export-button:hover {
  background: var(--accent-orange);
  color: var(--text-white);
  border-color: var(--accent-orange);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.notification-button.has-notifications {
  background: var(--gradient-orange);
  color: var(--text-white);
  border-color: var(--accent-orange);
}

/* Profile Button */
.profile-button {
  display: flex;
  align-items: center;
  gap: 10px;
  background: var(--gradient-primary);
  color: var(--text-white);
  border-radius: 16px;
  padding: 12px 20px;
  font-size: 15px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: var(--transition-smooth);
  box-shadow: var(--shadow-sm);
}

.profile-button:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.profile-button mat-icon {
  font-size: 24px;
}

/* Performance Overview */
.performance-overview {
  margin: 24px;
  margin-bottom: 32px;
}

.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.performance-card {
  background: var(--surface-primary);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  padding: 28px;
  position: relative;
  overflow: hidden;
  transition: var(--transition-smooth);
  box-shadow: var(--shadow-sm);
}

.performance-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.performance-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-orange);
}

.performance-card.excellence::before {
  background: linear-gradient(135deg, #22c55e, #16a34a);
}

.performance-card.growth::before {
  background: linear-gradient(135deg, var(--primary-navy), var(--secondary-navy));
}

.performance-card.satisfaction::before {
  background: linear-gradient(135deg, var(--accent-orange), var(--accent-orange-dark));
}

.performance-card.response::before {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.performance-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.performance-header mat-icon {
  font-size: 24px;
  color: var(--accent-orange);
}

.performance-header span {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.performance-value {
  font-size: 36px;
  font-weight: 800;
  color: var(--primary-navy);
  margin-bottom: 16px;
  line-height: 1;
}

.performance-indicator {
  height: 6px;
  background: var(--border-light);
  border-radius: 3px;
  position: relative;
  overflow: hidden;
}

.performance-indicator::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: var(--gradient-orange);
  border-radius: 3px;
  transition: width 1s ease-out;
}

/* Dynamic Content Area */
.dynamic-content {
  margin: 24px;
  margin-bottom: 32px;
}

.overview-header,
.section-header {
  margin-bottom: 32px;
  text-align: center;
}

.overview-header h2,
.section-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: var(--primary-navy);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 8px;
}

.overview-header h2 mat-icon,
.section-header h2 mat-icon {
  font-size: 32px;
  color: var(--accent-orange);
}

.overview-header p,
.section-header p {
  font-size: 16px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Site Statistics Grid */
.site-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.site-stat-card {
  background: var(--surface-primary);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  padding: 32px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: var(--transition-smooth);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.site-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-orange);
}

.site-stat-card.users::before {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.site-stat-card.companies::before {
  background: linear-gradient(135deg, var(--primary-navy), var(--secondary-navy));
}

.site-stat-card.jobs::before {
  background: linear-gradient(135deg, var(--accent-orange), var(--accent-orange-dark));
}

.site-stat-card.applications::before {
  background: linear-gradient(135deg, #22c55e, #16a34a);
}

.site-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  width: 64px;
  height: 64px;
  background: var(--gradient-orange);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon mat-icon {
  font-size: 32px;
  color: var(--text-white);
}

.stat-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.stat-value {
  font-size: 36px;
  font-weight: 800;
  color: var(--primary-navy);
  margin-bottom: 8px;
  line-height: 1;
}

.stat-change {
  font-size: 14px;
  font-weight: 500;
}

.stat-change.positive {
  color: #22c55e;
}

.stat-change.neutral {
  color: var(--text-secondary);
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: var(--gradient-orange);
  color: var(--text-white);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 11px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite ease-in-out;
  border: 2px solid var(--surface-primary);
}

/* Specific View Styles */
.workspace-stats,
.posts-stats,
.candidates-stats,
.test-results-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.workspace-card,
.posts-card,
.candidates-card,
.test-card {
  background: var(--surface-primary);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  padding: 32px;
  text-align: center;
  transition: var(--transition-smooth);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.workspace-card::before,
.posts-card::before,
.candidates-card::before,
.test-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-orange);
}

.workspace-card:hover,
.posts-card:hover,
.candidates-card:hover,
.test-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.workspace-card h3,
.posts-card h3,
.candidates-card h3,
.test-card h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.big-number {
  font-size: 48px;
  font-weight: 800;
  color: var(--primary-navy);
  line-height: 1;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.spinning {
  animation: spin 1.2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Notifications Panel */
.notifications-panel {
  position: absolute;
  top: 90px;
  right: 28px;
  width: 440px;
  max-height: 640px;
  background: var(--card-bg);
  border-radius: 20px;
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  z-index: 1000;
}

.notifications-header {
  padding: 18px 24px;
  background: var(--primary-purple);
  color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notifications-header h3 {
  font-size: 20px;
  font-weight: 600;
}

.notifications-list {
  max-height: 540px;
  overflow-y: auto;
  padding: 18px;
}

.notification-item {
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 14px;
  margin-bottom: 10px;
  background: var(--background-light);
  border-radius: 10px;
  transition: var(--transition);
}

.notification-item:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-sm);
}

.notification-item.success { border-left: 5px solid #22c55e; }
.notification-item.info { border-left: 5px solid var(--accent-gold); }
.notification-item.warning { border-left: 5px solid #f59e0b; }
.notification-item.error { border-left: 5px solid #ef4444; }

.notification-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 5px 0;
}

.notification-content p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

.notification-time {
  font-size: 12px;
  color: var(--text-secondary);
  opacity: 0.8;
}

/* Card container */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 28px;
  padding: 28px 0;
}

/* Statistics Overview - ADNIA Style */
.stats-overview {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 25px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  border: 1px solid #E5E7EB;
  position: relative;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(43, 58, 103, 0.1);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.stat-label {
  font-size: 14px;
  font-weight: 500;
  color: #8E9AAF;
  margin: 0;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #2B3A67;
  margin: 10px 0;
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  color: #26A69A;
}

.stat-change.positive {
  color: #26A69A;
}

.stat-change.negative {
  color: #FF6B35;
}

/* Quick Actions */
.quick-actions {
  padding: 28px 0;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-purple);
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 28px;
}

.section-title mat-icon {
  font-size: 28px;
  color: var(--accent-gold);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
  gap: 28px;
}

.action-card {
  background: var(--card-bg);
  border-radius: 20px;
  padding: 28px;
  display: flex;
  align-items: center;
  gap: 18px;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
}

.action-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.action-icon {
  width: 60px;
  height: 60px;
  background: var(--accent-gold);
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon mat-icon {
  font-size: 30px;
  color: var(--text-primary);
}

.action-content h3 {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-purple);
  margin: 0 0 5px 0;
}

.action-content p {
  font-size: 15px;
  color: var(--text-secondary);
}

.action-arrow {
  font-size: 20px;
  color: var(--text-secondary);
  transition: var(--transition);
}

.action-card:hover .action-arrow {
  color: var(--accent-gold);
  transform: translateX(8px);
}

/* Recent Activity */
.recent-activity {
  padding: 28px 0;
}

.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(440px, 1fr));
  gap: 28px;
}

.activity-section {
  background: var(--card-bg);
  border-radius: 20px;
  padding: 28px;
  box-shadow: var(--shadow-md);
  transition: var(--transition);
}

.activity-section:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

.activity-title {
  font-size: 22px;
  font-weight: 700;
  color: var(--primary-purple);
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 24px;
}

.activity-title mat-icon {
  font-size: 28px;
  color: var(--accent-gold);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 18px;
  padding: 18px;
  background: var(--background-light);
  border-radius: 12px;
  transition: var(--transition);
}

.activity-item:hover {
  transform: translateX(8px);
  background: rgba(250, 204, 21, 0.1);
}

.activity-icon {
  width: 52px;
  height: 52px;
  background: rgba(250, 204, 21, 0.15);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-icon mat-icon {
  font-size: 26px;
  color: var(--accent-gold);
}

.activity-details h4 {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
}

.activity-details p {
  font-size: 15px;
  color: var(--text-secondary);
}

.activity-date {
  font-size: 13px;
  color: var(--text-secondary);
  opacity: 0.8;
}

.activity-status {
  padding: 6px 14px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
}

.activity-status.active {
  background: rgba(34, 197, 94, 0.15);
  color: #22c55e;
}

.activity-score {
  padding: 6px 14px;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  background: rgba(124, 58, 237, 0.15);
  color: var(--secondary-purple);
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 56px;
  color: var(--text-secondary);
}

.loading-spinner mat-icon {
  font-size: 40px;
  color: var(--accent-gold);
  animation: spin 1.3s linear infinite;
}

/* Dynamic Content Styles */
.dynamic-content {
  margin: 24px;
  margin-top: 0;
}

.dashboard-overview,
.workspace-view,
.posts-view,
.candidates-view,
.test-results-view {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.overview-header,
.section-header {
  margin-bottom: 32px;
  text-align: center;
}

.overview-header h2,
.section-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: var(--primary-navy);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 8px;
}

.overview-header p,
.section-header p {
  font-size: 16px;
  color: var(--text-secondary);
  font-weight: 500;
}

/* Site Statistics Grid */
.site-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.site-stat-card {
  background: var(--surface-primary);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  padding: 32px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: var(--transition-smooth);
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
}

.site-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-orange);
}

.site-stat-card.users::before {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.site-stat-card.companies::before {
  background: linear-gradient(135deg, var(--primary-navy), var(--secondary-navy));
}

.site-stat-card.jobs::before {
  background: linear-gradient(135deg, var(--accent-orange), var(--accent-orange-dark));
}

.site-stat-card.applications::before {
  background: linear-gradient(135deg, #10b981, #059669);
}

.site-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.site-stat-card .stat-icon {
  width: 64px;
  height: 64px;
  background: var(--gradient-orange);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.site-stat-card .stat-icon mat-icon {
  font-size: 32px;
  color: var(--text-white);
}

.site-stat-card .stat-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.site-stat-card .stat-value {
  font-size: 36px;
  font-weight: 800;
  color: var(--primary-navy);
  margin-bottom: 4px;
  line-height: 1;
}

.site-stat-card .stat-change {
  font-size: 14px;
  font-weight: 500;
}

.site-stat-card .stat-change.positive {
  color: #10b981;
}

.site-stat-card .stat-change.neutral {
  color: var(--text-secondary);
}

/* View-specific styles */
.workspace-stats,
.posts-stats,
.candidates-stats,
.test-results-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.workspace-card,
.posts-card,
.candidates-card,
.test-card {
  background: var(--surface-primary);
  border: 1px solid var(--border-light);
  border-radius: 16px;
  padding: 28px;
  text-align: center;
  transition: var(--transition-smooth);
  box-shadow: var(--shadow-sm);
}

.workspace-card:hover,
.posts-card:hover,
.candidates-card:hover,
.test-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.workspace-card h3,
.posts-card h3,
.candidates-card h3,
.test-card h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.big-number {
  font-size: 48px;
  font-weight: 800;
  color: var(--primary-navy);
  line-height: 1;
}

/* Responsive */
@media (max-width: 1200px) {
  .dashboard {
    grid-template-columns: 250px 1fr;
  }

  .search-container {
    width: 320px;
  }
}

@media (max-width: 992px) {
  .dashboard {
    grid-template-columns: 1fr;
    grid-template-areas: "main";
  }

  app-sidebar-recruiter {
    display: none;
  }

  .dashboard-main {
    margin: 12px;
    padding: 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .search-container {
    width: 100%;
  }
}

@media (max-width: 768px) {
  .stats-grid,
  .actions-grid,
  .activity-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-main {
    margin: 8px;
    padding: 16px;
    border-radius: 16px;
  }

  .dashboard-header {
    padding: 12px;
  }

  .stats-overview,
  .quick-actions,
  .recent-activity {
    padding: 12px 0;
  }
}
