/* Simple Professional Dashboard */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

:root {
  /* Professional Colors */
  --primary: #001040FF;
  --secondary: #001660FF;
  --accent: #FF6B35;
  
  /* Clean Backgrounds */
  --white: #FFFFFF;
  --light: #F8FAFC;
  --lighter: #F1F5F9;
  
  /* Text */
  --text-dark: #1E293B;
  --text-gray: #64748B;
  --text-light: #94A3B8;
  
  /* Borders */
  --border: #E2E8F0;
  
  /* Simple Shadows */
  --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  background: var(--light);
  color: var(--text-dark);
  line-height: 1.6;
}

/* Dashboard Layout */
.dashboard {
  display: grid;
  grid-template-columns: 280px 1fr;
  min-height: 100vh;
  background: var(--light);
}

/* Main Content */
.main-content {
  background: var(--light);
  padding: 32px;
  overflow-x: hidden;
}

/* Header */
.dashboard-header {
  background: var(--white);
  border-bottom: 1px solid var(--border);
  margin-bottom: 32px;
  border-radius: 12px;
  box-shadow: var(--shadow);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
}

.header-left h1 {
  font-size: 28px;
  font-weight: 700;
  color: var(--primary);
  margin: 0 0 4px 0;
}

.header-left p {
  font-size: 16px;
  color: var(--text-gray);
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Search */
.search-container {
  display: flex;
  align-items: center;
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 12px 16px;
  width: 320px;
  transition: all 0.2s ease;
}

.search-container:focus-within {
  border-color: var(--accent);
  box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.search-icon {
  color: var(--text-light);
  font-size: 20px;
  margin-right: 12px;
}

.search-bar {
  border: none;
  background: transparent;
  flex: 1;
  font-size: 15px;
  color: var(--text-dark);
  outline: none;
  font-family: inherit;
}

.search-bar::placeholder {
  color: var(--text-light);
}

/* Buttons */
.notification-button,
.refresh-button,
.export-button {
  width: 44px;
  height: 44px;
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-gray);
  cursor: pointer;
  transition: all 0.2s ease;
}

.notification-button:hover,
.refresh-button:hover,
.export-button:hover {
  background: var(--accent);
  color: var(--white);
  border-color: var(--accent);
  transform: translateY(-1px);
}

.profile-button {
  display: flex;
  align-items: center;
  gap: 12px;
  background: var(--primary);
  color: var(--white);
  border-radius: 8px;
  padding: 12px 20px;
  font-size: 15px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.profile-button:hover {
  background: var(--secondary);
  transform: translateY(-1px);
}

/* Content Area */
.dynamic-content {
  margin-bottom: 32px;
}

/* Simple Cards */
.card {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: var(--shadow);
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

/* Stats Grid */
.stats-grid,
.executive-summary,
.site-stats-grid,
.workspace-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card,
.summary-card,
.site-stat-card {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before,
.summary-card::before,
.site-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--accent);
}

.stat-card:hover,
.summary-card:hover,
.site-stat-card:hover {
  border-color: var(--accent);
  box-shadow: var(--shadow-md);
  transform: translateY(-4px);
}

.stat-value,
.main-value {
  font-size: 36px;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 8px;
  line-height: 1;
}

.stat-label,
.card-title {
  font-size: 16px;
  color: var(--text-gray);
  font-weight: 600;
  margin-bottom: 12px;
}

.stat-change,
.value-change {
  font-size: 14px;
  font-weight: 500;
  margin-top: 8px;
}

.stat-change.positive,
.value-change.positive {
  color: #10b981;
}

.stat-change.negative {
  color: #ef4444;
}

.stat-change.neutral {
  color: var(--text-light);
}

/* Section Headers */
.section-header,
.workspace-header,
.posts-header,
.candidates-header,
.test-results-header {
  margin-bottom: 24px;
}

.section-header h2,
.workspace-header h2,
.posts-header h2,
.candidates-header h2,
.test-results-header h2 {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary);
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-header h2 mat-icon {
  font-size: 28px;
  color: var(--accent);
}

.section-header p {
  font-size: 16px;
  color: var(--text-gray);
  margin: 0;
}

/* Lists */
.list-container,
.posts-list,
.candidates-list,
.test-results-list {
  background: var(--white);
  border: 1px solid var(--border);
  border-radius: 12px;
  overflow: hidden;
}

.list-item,
.post-card,
.candidate-card,
.test-result-card {
  padding: 20px;
  border-bottom: 1px solid var(--border);
  transition: all 0.2s ease;
}

.list-item:last-child,
.post-card:last-child,
.candidate-card:last-child,
.test-result-card:last-child {
  border-bottom: none;
}

.list-item:hover,
.post-card:hover,
.candidate-card:hover,
.test-result-card:hover {
  background: var(--lighter);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
}

.btn,
.action-btn {
  padding: 8px 16px;
  border: 1px solid var(--border);
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background: var(--white);
  color: var(--text-gray);
}

.btn-primary,
.action-btn.primary {
  background: var(--primary);
  color: var(--white);
  border-color: var(--primary);
}

.btn-primary:hover,
.action-btn.primary:hover {
  background: var(--secondary);
}

.btn-accent,
.action-btn.accept {
  background: var(--accent);
  color: var(--white);
  border-color: var(--accent);
}

.btn:hover,
.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow);
}

/* Status Badges */
.status-badge {
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.pending {
  background: #fef3c7;
  color: #92400e;
}

.status-badge.accepted {
  background: #dbeafe;
  color: #1e40af;
}

.status-badge.rejected {
  background: #fee2e2;
  color: #991b1b;
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--text-gray);
}

.empty-state mat-icon {
  font-size: 64px;
  color: var(--text-light);
  margin-bottom: 20px;
}

.empty-state h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-dark);
}

.empty-state p {
  font-size: 16px;
  margin-bottom: 24px;
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard {
    grid-template-columns: 1fr;
  }

  .main-content {
    padding: 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .search-container {
    width: 100%;
  }

  .stats-grid,
  .executive-summary,
  .site-stats-grid,
  .workspace-stats-grid {
    grid-template-columns: 1fr;
  }
}
