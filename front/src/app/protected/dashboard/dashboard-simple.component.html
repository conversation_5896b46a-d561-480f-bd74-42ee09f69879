<div class="dashboard">
  <app-sidebar-recruiter (viewChange)="onViewChange($event)"></app-sidebar-recruiter>

  <main class="main-content">
    <!-- Simple Header -->
    <header class="dashboard-header">
      <div class="header-content">
        <div class="header-left">
          <h1>Dashboard Recruiter</h1>
          <p>Gérez vos recrutements et analysez vos performances</p>
        </div>
        <div class="header-right">
          <div class="search-container">
            <mat-icon class="search-icon">search</mat-icon>
            <input
              type="text"
              class="search-bar"
              placeholder="Rechercher..."
              (input)="filterDashboard($event)"
            />
          </div>
          <button class="notification-button" (click)="toggleNotifications()">
            <mat-icon>notifications</mat-icon>
          </button>
          <button class="refresh-button" (click)="refreshDashboard()">
            <mat-icon>refresh</mat-icon>
          </button>
          <button class="profile-button" (click)="goToProfile()">
            <mat-icon>person</mat-icon>
            Profil
          </button>
        </div>
      </div>
    </header>

    <!-- Dynamic Content -->
    <section class="dynamic-content" [ngSwitch]="currentView">

      <!-- Dashboard Overview (Default) -->
      <div *ngSwitchDefault class="dashboard-overview">
        <div class="section-header">
          <h2>
            <mat-icon>analytics</mat-icon>
            Vue d'ensemble
          </h2>
          <p>Statistiques générales de votre plateforme</p>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-value">{{ siteStats.totalUsers }}</div>
            <div class="stat-label">Utilisateurs Totaux</div>
            <div class="stat-change positive">+{{ siteStats.newUsersThisMonth }} ce mois</div>
          </div>

          <div class="stat-card">
            <div class="stat-value">{{ siteStats.activeCompanies }}</div>
            <div class="stat-label">Entreprises Actives</div>
            <div class="stat-change positive">+{{ siteStats.newCompaniesThisMonth }} nouvelles</div>
          </div>

          <div class="stat-card">
            <div class="stat-value">{{ siteStats.totalJobs }}</div>
            <div class="stat-label">Offres d'Emploi</div>
            <div class="stat-change neutral">{{ siteStats.activeJobs }} actives</div>
          </div>

          <div class="stat-card">
            <div class="stat-value">{{ siteStats.totalApplications }}</div>
            <div class="stat-label">Candidatures</div>
            <div class="stat-change positive">{{ siteStats.applicationsToday }} aujourd'hui</div>
          </div>
        </div>
      </div>

      <!-- Workspace View -->
      <div *ngSwitchCase="'workspace'" class="workspace-view">
        <div class="section-header">
          <h2>
            <mat-icon>business</mat-icon>
            Profil du Workspace
          </h2>
          <p>Informations et statistiques de votre workspace actif</p>
        </div>

        <div class="card" *ngIf="activeWorkspaceProfile">
          <h3>{{ activeWorkspaceProfile.name || 'Mon Workspace' }}</h3>
          <p>{{ activeWorkspaceProfile.description || 'Espace de travail professionnel' }}</p>
          
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-value">{{ workspaceStats.activePosts }}</div>
              <div class="stat-label">Offres Actives</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ workspaceStats.totalApplications }}</div>
              <div class="stat-label">Candidatures</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ workspaceStats.completedTests }}</div>
              <div class="stat-label">Tests Complétés</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Posts View -->
      <div *ngSwitchCase="'posts'" class="posts-view">
        <div class="section-header">
          <h2>
            <mat-icon>article</mat-icon>
            Mes Offres d'Emploi
          </h2>
          <p>Gérez vos offres publiées dans votre workspace</p>
        </div>

        <div class="list-container" *ngIf="workspacePosts.length > 0; else noPosts">
          <div class="list-item" *ngFor="let post of workspacePosts">
            <div class="item-info">
              <div class="item-avatar">
                <mat-icon>work</mat-icon>
              </div>
              <div class="item-details">
                <h4>{{ post.titre }}</h4>
                <p>{{ post.entreprise }} • {{ post.contractType }}</p>
              </div>
            </div>
            <div class="item-actions">
              <button class="btn btn-secondary" (click)="viewPost(post)">Voir</button>
              <button class="btn btn-primary" (click)="editPost(post)">Modifier</button>
            </div>
          </div>
        </div>

        <ng-template #noPosts>
          <div class="card">
            <div class="empty-state">
              <mat-icon>work_off</mat-icon>
              <h3>Aucune offre d'emploi</h3>
              <p>Vous n'avez pas encore publié d'offres dans ce workspace.</p>
              <button class="btn btn-primary" (click)="createNewPost()">Créer une offre</button>
            </div>
          </div>
        </ng-template>
      </div>

      <!-- Candidates View -->
      <div *ngSwitchCase="'candidates'" class="candidates-view">
        <div class="section-header">
          <h2>
            <mat-icon>people</mat-icon>
            Candidats du Workspace
          </h2>
          <p>Gérez les candidatures pour vos offres</p>
        </div>

        <div class="list-container" *ngIf="workspaceCandidates.length > 0; else noCandidates">
          <div class="list-item" *ngFor="let candidate of filteredCandidates">
            <div class="item-info">
              <div class="item-avatar">
                <img [src]="candidate.profileImage || '/assets/default-avatar.png'" 
                     [alt]="candidate.name" 
                     (click)="viewCandidateProfile(candidate)">
              </div>
              <div class="item-details">
                <h4 (click)="viewCandidateProfile(candidate)">{{ candidate.name }}</h4>
                <p>{{ candidate.email }} • {{ candidate.postTitle }}</p>
              </div>
            </div>
            <div class="item-actions">
              <span class="status-badge" [class]="candidate.status || 'pending'">
                {{ getCandidateStatusLabel(candidate.status) }}
              </span>
              <button class="btn btn-primary" (click)="viewCandidateProfile(candidate)">Voir Profil</button>
            </div>
          </div>
        </div>

        <ng-template #noCandidates>
          <div class="card">
            <div class="empty-state">
              <mat-icon>people_outline</mat-icon>
              <h3>Aucun candidat trouvé</h3>
              <p>Aucune candidature n'a été reçue pour vos offres.</p>
            </div>
          </div>
        </ng-template>
      </div>

      <!-- Test Results View -->
      <div *ngSwitchCase="'testResults'" class="test-results-view">
        <div class="section-header">
          <h2>
            <mat-icon>assessment</mat-icon>
            Résultats des Tests
          </h2>
          <p>Analysez les performances des candidats</p>
        </div>

        <div class="list-container" *ngIf="workspaceTestResults.length > 0; else noTestResults">
          <div class="list-item" *ngFor="let result of filteredTestResults">
            <div class="item-info">
              <div class="item-avatar">
                <img [src]="result.candidate?.profileImage || '/assets/default-avatar.png'" 
                     [alt]="result.candidateName">
              </div>
              <div class="item-details">
                <h4>{{ result.candidateName }}</h4>
                <p>{{ result.testName }} • {{ result.postTitle }}</p>
              </div>
            </div>
            <div class="item-actions">
              <span class="status-badge" [class]="getScoreClass(result.scorePercentage)">
                {{ result.scorePercentage }}%
              </span>
              <button class="btn btn-primary" (click)="viewTestDetails(result)">Détails</button>
            </div>
          </div>
        </div>

        <ng-template #noTestResults>
          <div class="card">
            <div class="empty-state">
              <mat-icon>quiz</mat-icon>
              <h3>Aucun résultat de test</h3>
              <p>Aucun test n'a été complété pour les offres de ce workspace.</p>
            </div>
          </div>
        </ng-template>
      </div>

    </section>
  </main>
</div>
