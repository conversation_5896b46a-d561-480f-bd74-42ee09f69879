import {Component, OnInit} from '@angular/core';
import {<PERSON><PERSON><PERSON>, Mat<PERSON>ard<PERSON>ontent, MatCardHeader, MatCardTitle} from '@angular/material/card';
import {MatIcon} from '@angular/material/icon';
import {MatButton, MatIconButton} from '@angular/material/button';
import {Router, RouterOutlet} from '@angular/router';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {SidebarRecruiterComponent} from '../sidebar-recruiter/sidebar-recruiter.component';
import {PostsService} from '../../services/posts/posts.service';
import {TestResultService} from '../../services/test-result/test-result.service';
import {JobApplicationService} from '../../services/job-application/job-application.service';
import {UserProfileService} from '../../services/user-profile/user-profile.service';
import {forkJoin, of} from 'rxjs';
import {catchError} from 'rxjs/operators';
import {trigger, state, style, transition, animate} from '@angular/animations';

@Component({
  selector: 'app-dashboard',
  imports: [
    CommonModule,
    FormsModule,
    MatCard,
    MatCardHeader,
    MatCardContent,
    MatIcon,
    MatButton,
    MatIconButton,
    MatCardTitle,
    RouterOutlet,
    SidebarRecruiterComponent,
  ],
  templateUrl: './dashboard.component.html',
  standalone: true,
  styleUrl: './dashboard.component.css',
  animations: [
    trigger('slideIn', [
      transition(':enter', [
        style({ transform: 'translateX(100%)', opacity: 0 }),
        animate('300ms ease-in-out', style({ transform: 'translateX(0)', opacity: 1 }))
      ]),
      transition(':leave', [
        animate('300ms ease-in-out', style({ transform: 'translateX(100%)', opacity: 0 }))
      ])
    ])
  ]
})
export class DashboardComponent implements OnInit {
  // Current view state
  currentView: string = 'dashboard';

  // Site Statistics
  siteStats = {
    totalUsers: 0,
    newUsersThisMonth: 0,
    activeCompanies: 0,
    newCompaniesThisMonth: 0,
    totalJobs: 0,
    activeJobs: 0,
    totalApplications: 0,
    applicationsToday: 0
  };

  // Workspace Statistics
  workspaceStats = {
    activeWorkspaces: 0,
    totalMembers: 0,
    activeProjects: 0
  };

  // Posts Statistics
  postsStats = {
    totalPosts: 0,
    totalViews: 0,
    conversionRate: 0
  };

  // Candidates Statistics
  candidatesStats = {
    totalCandidates: 0,
    pendingCandidates: 0,
    acceptedCandidates: 0
  };

  // Test Statistics
  testStats = {
    completedTests: 0,
    averageScore: 0,
    successRate: 0
  };

  // Dashboard Statistics with animation
  dashboardStats = {
    totalPosts: 0,
    activePosts: 0,
    totalCandidates: 0,
    pendingCandidates: 0,
    totalTests: 0,
    completedTests: 0,
    averageScore: 0,
    recentActivity: 0
  };

  // Animated stats for counter effect
  animatedStats = {
    totalPosts: 0,
    activePosts: 0,
    totalCandidates: 0,
    pendingCandidates: 0,
    totalTests: 0,
    completedTests: 0,
    averageScore: 0,
    recentActivity: 0
  };

  // Performance metrics
  performanceMetrics = {
    growthRate: 0,
    efficiency: 0,
    satisfaction: 0,
    responseTime: 0
  };

  // Real-time notifications
  notifications: any[] = [];
  showNotifications = false;

  // Recent data
  recentCandidates: any[] = [];
  recentTests: any[] = [];
  recentPosts: any[] = [];

  // Loading states
  isLoading = true;
  statsLoading = true;

  // Quick action cards
  quickActions = [
    {
      title: 'Créer une offre',
      description: 'Publiez une nouvelle offre d\'emploi',
      icon: 'add_circle',
      color: '#001040FF',
      action: () => this.goToCreatePost()
    },
    {
      title: 'Voir les candidatures',
      description: 'Consultez les nouvelles candidatures',
      icon: 'people',
      color: '#FF6B35',
      action: () => this.goToCandidatures()
    },
    {
      title: 'Résultats des tests',
      description: 'Analysez les performances des candidats',
      icon: 'assessment',
      color: '#001660FF',
      action: () => this.goToTestResults()
    },
    {
      title: 'Workspace',
      description: 'Gérez votre espace de travail',
      icon: 'business',
      color: '#FF6B35',
      action: () => this.goToWorkspace()
    }
  ];

  constructor(
    private router: Router,
    private postsService: PostsService,
    private testResultService: TestResultService,
    private jobApplicationService: JobApplicationService,
    private userProfileService: UserProfileService
  ) {}

  // goToCandidatures() {
  //   this.router.navigate(['/candidatures']);
  // }

  goToWorkspace() {
    this.router.navigate(['/workspaces']);
  }

  goToPosts() {
    this.router.navigate(['/acceuilposts']);
  }

  goToMessages() {
    this.router.navigate(['/messages']);
  }

  goToStats() {
    this.router.navigate(['/stats']);
  }

  goToPayments() {
    this.router.navigate(['/payments']);
  }

  goToInvitations() {
    this.router.navigate(['/invitations']);
  }

  goToSettings() {
    this.router.navigate(['/settings']);
  }

  goToCreatePost() {

    this.router.navigate(['/posts']);
  }


  goToTeamManagement() {
    this.router.navigate(['/manage-users']);
  }

  goToFeedback() {
    this.router.navigate(['/feedback']);
  }

  navigateToProfile() {
    this.router.navigate(['/profile']);
  }

  // Methods to change dashboard views
  showWorkspaceView() {
    this.currentView = 'workspace';
    this.loadWorkspaceStats();
  }

  showPostsView() {
    this.currentView = 'posts';
    this.loadPostsStats();
  }

  showCandidatesView() {
    this.currentView = 'candidates';
    this.loadCandidatesStats();
  }

  showTestResultsView() {
    this.currentView = 'testResults';
    this.loadTestStats();
  }

  showDashboardView() {
    this.currentView = 'dashboard';
    this.loadSiteStats();
  }

  ngOnInit() {
    this.loadDashboardData();
    this.loadSiteStatistics();
  }

  // View Management Methods
  setCurrentView(view: string) {
    this.currentView = view;
    this.loadViewSpecificData(view);
  }

  loadViewSpecificData(view: string) {
    switch(view) {
      case 'workspace':
        this.loadWorkspaceStats();
        break;
      case 'posts':
        this.loadPostsStats();
        break;
      case 'candidates':
        this.loadCandidatesStats();
        break;
      case 'testResults':
        this.loadTestStats();
        break;
      default:
        this.loadSiteStatistics();
    }
  }

  // Statistics Loading Methods
  loadSiteStatistics() {
    // Simulate loading site statistics
    this.siteStats = {
      totalUsers: 1247,
      newUsersThisMonth: 89,
      activeCompanies: 156,
      newCompaniesThisMonth: 12,
      totalJobs: 342,
      activeJobs: 187,
      totalApplications: 2156,
      applicationsToday: 23
    };
  }

  loadWorkspaceStats() {
    // Load workspace specific statistics
    this.workspaceStats = {
      activeWorkspaces: 8,
      totalMembers: 45,
      activeProjects: 23
    };
  }

  loadPostsStats() {
    // Load posts specific statistics
    this.postsStats = {
      totalPosts: this.dashboardStats.totalPosts,
      totalViews: 15420,
      conversionRate: 12.5
    };
  }

  loadCandidatesStats() {
    // Load candidates specific statistics
    this.candidatesStats = {
      totalCandidates: this.dashboardStats.totalCandidates,
      pendingCandidates: this.dashboardStats.pendingCandidates,
      acceptedCandidates: 89
    };
  }

  loadTestStats() {
    // Load test specific statistics
    this.testStats = {
      completedTests: this.dashboardStats.completedTests,
      averageScore: this.dashboardStats.averageScore,
      successRate: 78
    };
  }

  loadDashboardData() {
    this.isLoading = true;
    this.statsLoading = true;

    // Load all dashboard data in parallel
    forkJoin({
      posts: this.postsService.getMyPosts(0, 100).pipe(
        catchError(error => {
          console.error('Error loading posts:', error);
          return of({ content: [], totalElements: 0 });
        })
      ),
      testResults: this.testResultService.getAllTestResults().pipe(
        catchError(error => {
          console.error('Error loading test results:', error);
          return of([]);
        })
      )
    }).subscribe({
      next: (data) => {
        this.processPostsData(data.posts);
        this.processTestResultsData(data.testResults);
        this.calculateStats();
        this.isLoading = false;
        this.statsLoading = false;
      },
      error: (error) => {
        console.error('Error loading dashboard data:', error);
        this.isLoading = false;
        this.statsLoading = false;
      }
    });
  }

  // Search functionality for dashboard
  filterDashboard(event: Event) {
    const query = (event.target as HTMLInputElement).value.toLowerCase();
    // Filter quick actions based on search query
    if (query) {
      this.quickActions = this.quickActions.filter(action =>
        action.title.toLowerCase().includes(query) ||
        action.description.toLowerCase().includes(query)
      );
    } else {
      // Reset to original quick actions
      this.quickActions = [
        {
          title: 'Créer une offre',
          description: 'Publiez une nouvelle offre d\'emploi',
          icon: 'add_circle',
          color: '#001040FF',
          action: () => this.goToCreatePost()
        },
        {
          title: 'Voir les candidatures',
          description: 'Consultez les nouvelles candidatures',
          icon: 'people',
          color: '#FF6B35',
          action: () => this.goToCandidatures()
        },
        {
          title: 'Résultats des tests',
          description: 'Analysez les performances des candidats',
          icon: 'assessment',
          color: '#001660FF',
          action: () => this.goToTestResults()
        },
        {
          title: 'Workspace',
          description: 'Gérez votre espace de travail',
          icon: 'business',
          color: '#FF6B35',
          action: () => this.goToWorkspace()
        }
      ];
    }
  }

  goToTestResults() {
    this.router.navigate(['/results'])
      .then(success => {
        if (success) {
          console.log('Navigation vers /results réussie');
          // Optionnel : Ajouter ici d’autres opérations à effectuer après la navigation
        } else {
          console.error('Navigation vers /results a échoué');
          // Optionnel : Afficher un message d’erreur ou gérer l’échec de la navigation
        }
      });
  }


  processPostsData(postsData: any) {
    if (postsData && postsData.content) {
      this.recentPosts = postsData.content.slice(0, 5);
      this.dashboardStats.totalPosts = postsData.totalElements || postsData.content.length;
      this.dashboardStats.activePosts = postsData.content.filter((post: any) => !post.archived).length;
    }
  }

  processTestResultsData(testResults: any[]) {
    if (testResults && Array.isArray(testResults)) {
      this.recentTests = testResults.slice(0, 5);
      this.dashboardStats.totalTests = testResults.length;
      this.dashboardStats.completedTests = testResults.filter(test => test.scorePercentage !== undefined).length;

      // Calculate average score
      const scoresSum = testResults.reduce((sum, test) => sum + (test.scorePercentage || 0), 0);
      this.dashboardStats.averageScore = testResults.length > 0 ? Math.round(scoresSum / testResults.length) : 0;
    }
  }

  calculateStats() {
    // Calculate recent activity (posts and tests from last 7 days)
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const recentPostsCount = this.recentPosts.filter(post =>
      new Date(post.datePublication) > oneWeekAgo
    ).length;

    const recentTestsCount = this.recentTests.filter(test =>
      new Date(test.submittedAt) > oneWeekAgo
    ).length;

    this.dashboardStats.recentActivity = recentPostsCount + recentTestsCount;

    // Calculate performance metrics
    this.calculatePerformanceMetrics();

    // Animate stats with counter effect
    this.animateStats();

    // Generate notifications
    this.generateNotifications();
  }

  calculatePerformanceMetrics() {
    // Growth rate calculation (mock data for demo)
    this.performanceMetrics.growthRate = Math.round(
      ((this.dashboardStats.activePosts / Math.max(this.dashboardStats.totalPosts, 1)) * 100)
    );

    // Efficiency based on test completion rate
    this.performanceMetrics.efficiency = Math.round(
      ((this.dashboardStats.completedTests / Math.max(this.dashboardStats.totalTests, 1)) * 100)
    );

    // Satisfaction based on average score
    this.performanceMetrics.satisfaction = this.dashboardStats.averageScore;

    // Response time (mock data)
    this.performanceMetrics.responseTime = Math.round(Math.random() * 24 + 1);
  }

  animateStats() {
    // Animate each stat with a counter effect
    Object.keys(this.dashboardStats).forEach(key => {
      this.animateCounter(key as keyof typeof this.dashboardStats);
    });
  }

  animateCounter(statKey: keyof typeof this.dashboardStats) {
    const targetValue = this.dashboardStats[statKey];
    const duration = 2000; // 2 seconds
    const steps = 60;
    const stepValue = targetValue / steps;
    let currentStep = 0;

    const interval = setInterval(() => {
      currentStep++;
      this.animatedStats[statKey] = Math.round(stepValue * currentStep);

      if (currentStep >= steps) {
        this.animatedStats[statKey] = targetValue;
        clearInterval(interval);
      }
    }, duration / steps);
  }

  generateNotifications() {
    this.notifications = [];

    if (this.dashboardStats.recentActivity > 0) {
      this.notifications.push({
        type: 'success',
        title: 'Activité récente',
        message: `${this.dashboardStats.recentActivity} nouvelles activités cette semaine`,
        time: new Date(),
        icon: 'trending_up'
      });
    }

    if (this.dashboardStats.averageScore > 80) {
      this.notifications.push({
        type: 'success',
        title: 'Excellents résultats',
        message: `Score moyen de ${this.dashboardStats.averageScore}% aux tests`,
        time: new Date(),
        icon: 'star'
      });
    }

    if (this.dashboardStats.activePosts > 5) {
      this.notifications.push({
        type: 'info',
        title: 'Offres actives',
        message: `${this.dashboardStats.activePosts} offres d'emploi actives`,
        time: new Date(),
        icon: 'work'
      });
    }
  }

  // Navigation methods
  goToCandidatures() {
    this.router.navigate(['/candidatures']);
  }

  // Quick action methods
  executeQuickAction(action: () => void) {
    action();
  }

  // Utility methods
  getStatIcon(statType: string): string {
    const icons: { [key: string]: string } = {
      totalPosts: 'work',
      activePosts: 'trending_up',
      totalCandidates: 'people',
      pendingCandidates: 'hourglass_empty',
      totalTests: 'assignment',
      completedTests: 'assignment_turned_in',
      averageScore: 'grade',
      recentActivity: 'notifications'
    };
    return icons[statType] || 'info';
  }

  getStatColor(statType: string): string {
    const colors: { [key: string]: string } = {
      totalPosts: '#001040FF',
      activePosts: '#FF6B35',
      totalCandidates: '#001660FF',
      pendingCandidates: '#FF6B35',
      totalTests: '#001040FF',
      completedTests: '#FF6B35',
      averageScore: '#001660FF',
      recentActivity: '#FF6B35'
    };
    return colors[statType] || '#001040FF';
  }

  // Notification management
  toggleNotifications() {
    this.showNotifications = !this.showNotifications;
  }

  dismissNotification(index: number) {
    this.notifications.splice(index, 1);
  }

  getNotificationIcon(type: string): string {
    const icons: { [key: string]: string } = {
      success: 'check_circle',
      info: 'info',
      warning: 'warning',
      error: 'error'
    };
    return icons[type] || 'notifications';
  }

  // Advanced dashboard features
  refreshDashboard() {
    this.loadDashboardData();
  }

  exportDashboardData() {
    const data = {
      stats: this.dashboardStats,
      performance: this.performanceMetrics,
      recentPosts: this.recentPosts,
      recentTests: this.recentTests,
      exportDate: new Date()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `dashboard-data-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  // Performance indicators
  getPerformanceLevel(metric: string): string {
    const value = this.performanceMetrics[metric as keyof typeof this.performanceMetrics];
    if (value >= 80) return 'excellent';
    if (value >= 60) return 'good';
    if (value >= 40) return 'average';
    return 'poor';
  }

  getPerformanceColor(metric: string): string {
    const level = this.getPerformanceLevel(metric);
    const colors: { [key: string]: string } = {
      excellent: '#22c55e',
      good: '#3b82f6',
      average: '#f59e0b',
      poor: '#ef4444'
    };
    return colors[level] || '#9ca3af';
  }
}
