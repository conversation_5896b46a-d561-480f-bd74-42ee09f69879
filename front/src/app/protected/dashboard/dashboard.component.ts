import {Component, OnInit} from '@angular/core';
import {<PERSON><PERSON><PERSON>, <PERSON><PERSON>ard<PERSON>ontent, Mat<PERSON>ard<PERSON>eader, MatCardTitle} from '@angular/material/card';
import {MatIcon} from '@angular/material/icon';
import {Mat<PERSON>utton, MatIconButton} from '@angular/material/button';
import {Router, RouterOutlet} from '@angular/router';
import {CommonModule} from '@angular/common';
import {FormsModule} from '@angular/forms';
import {SidebarRecruiterComponent} from '../sidebar-recruiter/sidebar-recruiter.component';
import {PostsService} from '../../services/posts/posts.service';
import {TestResultService} from '../../services/test-result/test-result.service';
import {JobApplicationService} from '../../services/job-application/job-application.service';
import {UserProfileService} from '../../services/user-profile/user-profile.service';
import {forkJoin, of} from 'rxjs';
import {catchError} from 'rxjs/operators';
import {trigger, state, style, transition, animate} from '@angular/animations';

@Component({
  selector: 'app-dashboard',
  imports: [
    CommonModule,
    FormsModule,
    MatIcon,
    MatIconButton,
    SidebarRecruiterComponent,
  ],
  templateUrl: './dashboard.component.html',
  standalone: true,
  styleUrl: './dashboard.component.css',
  animations: [
    trigger('slideIn', [
      transition(':enter', [
        style({ transform: 'translateX(100%)', opacity: 0 }),
        animate('300ms ease-in-out', style({ transform: 'translateX(0)', opacity: 1 }))
      ]),
      transition(':leave', [
        animate('300ms ease-in-out', style({ transform: 'translateX(100%)', opacity: 0 }))
      ])
    ])
  ]
})
export class DashboardComponent implements OnInit {
  // Current view state
  currentView: string = 'dashboard';
  selectedPeriod: string = 'month';

  // Executive Dashboard Metrics
  monthlyRevenue: number = 0;
  revenueGrowth: number = 0;
  performanceScore: number = 0;
  performanceGrowth: number = 0;
  aiEfficiency: number = 0;
  aiGrowth: number = 0;
  npsScore: number = 0;
  npsGrowth: number = 0;

  // Advanced KPIs
  conversionRate: number = 0;
  avgRecruitmentTime: number = 0;
  candidateQuality: number = 0;
  recruitmentROI: number = 0;

  // Real-time Activities
  realtimeActivities: any[] = [];

  // UI State Management
  showSearchSuggestions: boolean = false;
  showProfileMenu: boolean = false;
  hasProfileImage: boolean = false;
  userName: string = 'Recruiter Pro';
  lastSyncTime: string = '';
  systemPerformance: number = 98;

  // Search Suggestions
  searchSuggestions = [
    { icon: 'person', text: 'Rechercher candidats' },
    { icon: 'work', text: 'Offres d\'emploi' },
    { icon: 'analytics', text: 'Rapports analytics' },
    { icon: 'assessment', text: 'Résultats tests' }
  ];

  // Active Workspace Data
  activeWorkspaceProfile: any = null;
  workspacePosts: any[] = [];
  workspaceCandidates: any[] = [];
  workspaceTestResults: any[] = [];

  // Filtered Data
  filteredCandidates: any[] = [];
  filteredTestResults: any[] = [];

  // Filters
  candidatesFilter: string = 'all';
  testResultsFilter: string = 'all';

  // Site Statistics
  siteStats = {
    totalUsers: 0,
    newUsersThisMonth: 0,
    activeCompanies: 0,
    newCompaniesThisMonth: 0,
    totalJobs: 0,
    activeJobs: 0,
    totalApplications: 0,
    applicationsToday: 0
  };

  // Workspace Statistics
  workspaceStats = {
    activeWorkspaces: 0,
    totalMembers: 0,
    activeProjects: 0,
    activePosts: 0,
    totalApplications: 0,
    newPostsThisMonth: 0,
    newApplicationsToday: 0,
    completedTests: 0,
    averageScore: 0,
    conversionRate: 0,
    conversionGrowth: 0
  };

  // Posts Statistics
  postsStats = {
    totalPosts: 0,
    totalViews: 0,
    conversionRate: 0
  };

  // Candidates Statistics
  candidatesStats = {
    totalCandidates: 0,
    pendingCandidates: 0,
    acceptedCandidates: 0
  };

  // Test Statistics
  testStats = {
    completedTests: 0,
    averageScore: 0,
    successRate: 0
  };

  // Dashboard Statistics with animation
  dashboardStats = {
    totalPosts: 0,
    activePosts: 0,
    totalCandidates: 0,
    pendingCandidates: 0,
    totalTests: 0,
    completedTests: 0,
    averageScore: 0,
    recentActivity: 0
  };

  // Animated stats for counter effect
  animatedStats = {
    totalPosts: 0,
    activePosts: 0,
    totalCandidates: 0,
    pendingCandidates: 0,
    totalTests: 0,
    completedTests: 0,
    averageScore: 0,
    recentActivity: 0
  };

  // Performance metrics
  performanceMetrics = {
    growthRate: 0,
    efficiency: 0,
    satisfaction: 0,
    responseTime: 0
  };

  // Real-time notifications
  notifications: any[] = [];
  showNotifications = false;

  // Recent data
  recentCandidates: any[] = [];
  recentTests: any[] = [];
  recentPosts: any[] = [];

  // Loading states
  isLoading = true;
  statsLoading = true;

  // Quick action cards
  quickActions = [
    {
      title: 'Créer une offre',
      description: 'Publiez une nouvelle offre d\'emploi',
      icon: 'add_circle',
      color: '#001040FF',
      action: () => this.goToCreatePost()
    },
    {
      title: 'Voir les candidatures',
      description: 'Consultez les nouvelles candidatures',
      icon: 'people',
      color: '#FF6B35',
      action: () => this.goToCandidatures()
    },
    {
      title: 'Résultats des tests',
      description: 'Analysez les performances des candidats',
      icon: 'assessment',
      color: '#001660FF',
      action: () => this.goToTestResults()
    },
    {
      title: 'Workspace',
      description: 'Gérez votre espace de travail',
      icon: 'business',
      color: '#FF6B35',
      action: () => this.goToWorkspace()
    }
  ];

  constructor(
    private router: Router,
    private postsService: PostsService,
    private testResultService: TestResultService,
    private jobApplicationService: JobApplicationService,
    private userProfileService: UserProfileService
  ) {}

  // goToCandidatures() {
  //   this.router.navigate(['/candidatures']);
  // }

  goToWorkspace() {
    this.router.navigate(['/workspaces']);
  }

  goToPosts() {
    this.router.navigate(['/acceuilposts']);
  }

  goToMessages() {
    this.router.navigate(['/messages']);
  }

  goToStats() {
    this.router.navigate(['/stats']);
  }

  goToPayments() {
    this.router.navigate(['/payments']);
  }

  goToInvitations() {
    this.router.navigate(['/invitations']);
  }

  goToSettings() {
    this.router.navigate(['/settings']);
  }

  goToCreatePost() {

    this.router.navigate(['/posts']);
  }


  goToTeamManagement() {
    this.router.navigate(['/manage-users']);
  }

  goToFeedback() {
    this.router.navigate(['/feedback']);
  }

  navigateToProfile() {
    this.router.navigate(['/profile']);
  }

  // Methods to change dashboard views
  showWorkspaceView() {
    this.currentView = 'workspace';
    this.loadWorkspaceStatsData();
  }

  showPostsView() {
    this.currentView = 'posts';
    this.loadPostsStatsData();
  }

  showCandidatesView() {
    this.currentView = 'candidates';
    this.loadCandidatesStatsData();
  }

  showTestResultsView() {
    this.currentView = 'testResults';
    this.loadTestStatsData();
  }

  showDashboardView() {
    this.currentView = 'dashboard';
    this.loadSiteStats();
  }

  // Handle view changes from sidebar
  onViewChange(view: string) {
    this.currentView = view;
    switch(view) {
      case 'workspace':
        this.loadWorkspaceStatsData();
        break;
      case 'posts':
        this.loadPostsStatsData();
        break;
      case 'candidates':
        this.loadCandidatesStatsData();
        break;
      case 'testResults':
        this.loadTestStatsData();
        break;
      default:
        this.loadSiteStats();
        break;
    }
  }

  ngOnInit() {
    this.loadDashboardData();
    this.loadSiteStatistics();
    this.loadExecutiveMetrics();
    this.loadAdvancedKPIs();
    this.loadRealtimeActivities();
    this.startRealtimeUpdates();
    this.initializeUI();
  }

  initializeUI() {
    this.lastSyncTime = new Date().toLocaleTimeString('fr-FR');
    this.updateSystemStatus();
  }

  updateSystemStatus() {
    // Simulate system performance updates
    setInterval(() => {
      this.systemPerformance = 95 + Math.random() * 5;
      this.lastSyncTime = new Date().toLocaleTimeString('fr-FR');
    }, 60000); // Update every minute
  }

  // Date and Time Methods
  getCurrentDate(): string {
    return new Date().toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  getCurrentTime(): string {
    return new Date().toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  // UI Interaction Methods
  toggleProfileMenu() {
    this.showProfileMenu = !this.showProfileMenu;
  }

  openAnalytics() {
    console.log('Opening analytics...');
    // Navigate to analytics page or open modal
  }

  openSettings() {
    console.log('Opening settings...');
    this.showProfileMenu = false;
  }

  logout() {
    console.log('Logging out...');
    // Implement logout logic
  }

  // Action Methods for Stats Cards
  viewRevenueDetails() {
    console.log('Viewing revenue details...');
  }

  exportRevenue() {
    console.log('Exporting revenue data...');
  }

  viewCandidates() {
    this.currentView = 'candidates';
    this.loadCandidatesStatsData();
  }

  createJob() {
    console.log('Creating new job...');
  }

  viewFeedback() {
    console.log('Viewing feedback...');
  }

  // Executive Dashboard Methods
  loadExecutiveMetrics() {
    // Simulate loading executive metrics - replace with real API calls
    this.monthlyRevenue = 125000;
    this.revenueGrowth = 12.5;
    this.performanceScore = 94;
    this.performanceGrowth = 8.2;
    this.aiEfficiency = 87;
    this.aiGrowth = 15.3;
    this.npsScore = 72;
    this.npsGrowth = 5;
  }

  loadAdvancedKPIs() {
    // Simulate loading advanced KPIs
    this.conversionRate = 23.5;
    this.avgRecruitmentTime = 18;
    this.candidateQuality = 89;
    this.recruitmentROI = 156;
  }

  loadRealtimeActivities() {
    // Simulate real-time activities
    this.realtimeActivities = [
      {
        type: 'success',
        icon: 'person_add',
        message: 'Nouveau candidat inscrit - Marie Dubois',
        timestamp: new Date(),
        status: 'completed'
      },
      {
        type: 'info',
        icon: 'work',
        message: 'Nouvelle offre publiée - Développeur Senior',
        timestamp: new Date(Date.now() - 300000),
        status: 'active'
      },
      {
        type: 'warning',
        icon: 'schedule',
        message: 'Entretien programmé dans 30 minutes',
        timestamp: new Date(Date.now() - 600000),
        status: 'pending'
      },
      {
        type: 'success',
        icon: 'check_circle',
        message: 'Test complété avec succès - Score: 85%',
        timestamp: new Date(Date.now() - 900000),
        status: 'completed'
      },
      {
        type: 'info',
        icon: 'email',
        message: '5 nouvelles candidatures reçues',
        timestamp: new Date(Date.now() - 1200000),
        status: 'new'
      }
    ];
  }

  startRealtimeUpdates() {
    // Simulate real-time updates every 30 seconds
    setInterval(() => {
      this.updateRealtimeData();
    }, 30000);
  }

  updateRealtimeData() {
    // Simulate updating metrics
    this.performanceScore += Math.random() * 2 - 1;
    this.performanceScore = Math.max(0, Math.min(100, this.performanceScore));

    this.aiEfficiency += Math.random() * 1.5 - 0.75;
    this.aiEfficiency = Math.max(0, Math.min(100, this.aiEfficiency));
  }

  // Utility Methods
  formatNumber(num: number): string {
    return new Intl.NumberFormat('fr-FR').format(num);
  }

  updateKPIs() {
    // Update KPIs based on selected period
    this.loadAdvancedKPIs();
  }

  refreshActivity() {
    this.loadRealtimeActivities();
  }

  toggleActivityFilter() {
    // Implement activity filtering
    console.log('Toggle activity filter');
  }

  // Load site statistics
  loadSiteStatistics() {
    this.loadSiteStats();
    this.loadWorkspaceStatsData();
    this.loadPostsStatsData();
    this.loadCandidatesStatsData();
    this.loadTestStatsData();
  }

  loadSiteStats() {
    // Simulate loading site statistics - replace with real API calls
    this.siteStats = {
      totalUsers: 1247,
      newUsersThisMonth: 89,
      activeCompanies: 156,
      newCompaniesThisMonth: 12,
      totalJobs: 342,
      activeJobs: 187,
      totalApplications: 2156,
      applicationsToday: 23
    };
  }

  loadWorkspaceStatsData() {
    // Simulate loading workspace statistics
    this.workspaceStats = {
      activeWorkspaces: 45,
      totalMembers: 234,
      activeProjects: 67
    };
  }

  loadPostsStatsData() {
    // Simulate loading posts statistics
    this.postsStats = {
      totalPosts: 342,
      totalViews: 15678,
      conversionRate: 12.5
    };
  }

  loadCandidatesStatsData() {
    // Simulate loading candidates statistics
    this.candidatesStats = {
      totalCandidates: 1847,
      pendingCandidates: 234,
      acceptedCandidates: 156
    };
  }

  loadTestStatsData() {
    // Simulate loading test statistics
    this.testStats = {
      completedTests: 456,
      averageScore: 78,
      successRate: 65
    };
  }

  loadDashboardData() {
    this.isLoading = true;
    this.statsLoading = true;

    // Load all dashboard data in parallel
    forkJoin({
      posts: this.postsService.getMyPosts(0, 100).pipe(
        catchError(error => {
          console.error('Error loading posts:', error);
          return of({ content: [], totalElements: 0 });
        })
      ),
      testResults: this.testResultService.getAllTestResults().pipe(
        catchError(error => {
          console.error('Error loading test results:', error);
          return of([]);
        })
      )
    }).subscribe({
      next: (data) => {
        this.processPostsData(data.posts);
        this.processTestResultsData(data.testResults);
        this.calculateStats();
        this.isLoading = false;
        this.statsLoading = false;
      },
      error: (error) => {
        console.error('Error loading dashboard data:', error);
        this.isLoading = false;
        this.statsLoading = false;
      }
    });
  }

  // Search functionality for dashboard
  filterDashboard(event: Event) {
    const query = (event.target as HTMLInputElement).value.toLowerCase();
    // Filter quick actions based on search query
    if (query) {
      this.quickActions = this.quickActions.filter(action =>
        action.title.toLowerCase().includes(query) ||
        action.description.toLowerCase().includes(query)
      );
    } else {
      // Reset to original quick actions
      this.quickActions = [
        {
          title: 'Créer une offre',
          description: 'Publiez une nouvelle offre d\'emploi',
          icon: 'add_circle',
          color: '#001040FF',
          action: () => this.goToCreatePost()
        },
        {
          title: 'Voir les candidatures',
          description: 'Consultez les nouvelles candidatures',
          icon: 'people',
          color: '#FF6B35',
          action: () => this.goToCandidatures()
        },
        {
          title: 'Résultats des tests',
          description: 'Analysez les performances des candidats',
          icon: 'assessment',
          color: '#001660FF',
          action: () => this.goToTestResults()
        },
        {
          title: 'Workspace',
          description: 'Gérez votre espace de travail',
          icon: 'business',
          color: '#FF6B35',
          action: () => this.goToWorkspace()
        }
      ];
    }
  }

  goToTestResults() {
    this.router.navigate(['/results'])
      .then(success => {
        if (success) {
          console.log('Navigation vers /results réussie');
          // Optionnel : Ajouter ici d’autres opérations à effectuer après la navigation
        } else {
          console.error('Navigation vers /results a échoué');
          // Optionnel : Afficher un message d’erreur ou gérer l’échec de la navigation
        }
      });
  }


  processPostsData(postsData: any) {
    if (postsData && postsData.content) {
      this.recentPosts = postsData.content.slice(0, 5);
      this.dashboardStats.totalPosts = postsData.totalElements || postsData.content.length;
      this.dashboardStats.activePosts = postsData.content.filter((post: any) => !post.archived).length;
    }
  }

  processTestResultsData(testResults: any[]) {
    if (testResults && Array.isArray(testResults)) {
      this.recentTests = testResults.slice(0, 5);
      this.dashboardStats.totalTests = testResults.length;
      this.dashboardStats.completedTests = testResults.filter(test => test.scorePercentage !== undefined).length;

      // Calculate average score
      const scoresSum = testResults.reduce((sum, test) => sum + (test.scorePercentage || 0), 0);
      this.dashboardStats.averageScore = testResults.length > 0 ? Math.round(scoresSum / testResults.length) : 0;
    }
  }

  calculateStats() {
    // Calculate recent activity (posts and tests from last 7 days)
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const recentPostsCount = this.recentPosts.filter(post =>
      new Date(post.datePublication) > oneWeekAgo
    ).length;

    const recentTestsCount = this.recentTests.filter(test =>
      new Date(test.submittedAt) > oneWeekAgo
    ).length;

    this.dashboardStats.recentActivity = recentPostsCount + recentTestsCount;

    // Calculate performance metrics
    this.calculatePerformanceMetrics();

    // Animate stats with counter effect
    this.animateStats();

    // Generate notifications
    this.generateNotifications();
  }

  calculatePerformanceMetrics() {
    // Growth rate calculation (mock data for demo)
    this.performanceMetrics.growthRate = Math.round(
      ((this.dashboardStats.activePosts / Math.max(this.dashboardStats.totalPosts, 1)) * 100)
    );

    // Efficiency based on test completion rate
    this.performanceMetrics.efficiency = Math.round(
      ((this.dashboardStats.completedTests / Math.max(this.dashboardStats.totalTests, 1)) * 100)
    );

    // Satisfaction based on average score
    this.performanceMetrics.satisfaction = this.dashboardStats.averageScore;

    // Response time (mock data)
    this.performanceMetrics.responseTime = Math.round(Math.random() * 24 + 1);
  }

  animateStats() {
    // Animate each stat with a counter effect
    Object.keys(this.dashboardStats).forEach(key => {
      this.animateCounter(key as keyof typeof this.dashboardStats);
    });
  }

  animateCounter(statKey: keyof typeof this.dashboardStats) {
    const targetValue = this.dashboardStats[statKey];
    const duration = 2000; // 2 seconds
    const steps = 60;
    const stepValue = targetValue / steps;
    let currentStep = 0;

    const interval = setInterval(() => {
      currentStep++;
      this.animatedStats[statKey] = Math.round(stepValue * currentStep);

      if (currentStep >= steps) {
        this.animatedStats[statKey] = targetValue;
        clearInterval(interval);
      }
    }, duration / steps);
  }

  generateNotifications() {
    this.notifications = [];

    if (this.dashboardStats.recentActivity > 0) {
      this.notifications.push({
        type: 'success',
        title: 'Activité récente',
        message: `${this.dashboardStats.recentActivity} nouvelles activités cette semaine`,
        time: new Date(),
        icon: 'trending_up'
      });
    }

    if (this.dashboardStats.averageScore > 80) {
      this.notifications.push({
        type: 'success',
        title: 'Excellents résultats',
        message: `Score moyen de ${this.dashboardStats.averageScore}% aux tests`,
        time: new Date(),
        icon: 'star'
      });
    }

    if (this.dashboardStats.activePosts > 5) {
      this.notifications.push({
        type: 'info',
        title: 'Offres actives',
        message: `${this.dashboardStats.activePosts} offres d'emploi actives`,
        time: new Date(),
        icon: 'work'
      });
    }
  }

  // Navigation methods
  goToCandidatures() {
    this.router.navigate(['/candidatures']);
  }

  // Quick action methods
  executeQuickAction(action: () => void) {
    action();
  }

  // Utility methods
  getStatIcon(statType: string): string {
    const icons: { [key: string]: string } = {
      totalPosts: 'work',
      activePosts: 'trending_up',
      totalCandidates: 'people',
      pendingCandidates: 'hourglass_empty',
      totalTests: 'assignment',
      completedTests: 'assignment_turned_in',
      averageScore: 'grade',
      recentActivity: 'notifications'
    };
    return icons[statType] || 'info';
  }

  getStatColor(statType: string): string {
    const colors: { [key: string]: string } = {
      totalPosts: '#001040FF',
      activePosts: '#FF6B35',
      totalCandidates: '#001660FF',
      pendingCandidates: '#FF6B35',
      totalTests: '#001040FF',
      completedTests: '#FF6B35',
      averageScore: '#001660FF',
      recentActivity: '#FF6B35'
    };
    return colors[statType] || '#001040FF';
  }

  // Notification management
  toggleNotifications() {
    this.showNotifications = !this.showNotifications;
  }

  dismissNotification(index: number) {
    this.notifications.splice(index, 1);
  }

  getNotificationIcon(type: string): string {
    const icons: { [key: string]: string } = {
      success: 'check_circle',
      info: 'info',
      warning: 'warning',
      error: 'error'
    };
    return icons[type] || 'notifications';
  }

  // Advanced dashboard features
  refreshDashboard() {
    this.loadDashboardData();
  }

  exportDashboardData() {
    const data = {
      stats: this.dashboardStats,
      performance: this.performanceMetrics,
      recentPosts: this.recentPosts,
      recentTests: this.recentTests,
      exportDate: new Date()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `dashboard-data-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  // Performance indicators
  getPerformanceLevel(metric: string): string {
    const value = this.performanceMetrics[metric as keyof typeof this.performanceMetrics];
    if (value >= 80) return 'excellent';
    if (value >= 60) return 'good';
    if (value >= 40) return 'average';
    return 'poor';
  }

  getPerformanceColor(metric: string): string {
    const level = this.getPerformanceLevel(metric);
    const colors: { [key: string]: string } = {
      excellent: '#22c55e',
      good: '#3b82f6',
      average: '#f59e0b',
      poor: '#ef4444'
    };
    return colors[level] || '#9ca3af';
  }
}
