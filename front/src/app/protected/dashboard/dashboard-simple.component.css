@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Simple Professional Dashboard Styles */
:root {
  /* Colors */
  --primary: #001040FF;
  --secondary: #001660FF;
  --accent: #FF6B35;
  
  /* Backgrounds */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8FAFC;
  --bg-light: #F1F5F9;
  
  /* Text */
  --text-primary: #1E293B;
  --text-secondary: #64748B;
  --text-muted: #94A3B8;
  --text-white: #FFFFFF;
  
  /* Borders */
  --border: #E2E8F0;
  --border-light: #F1F5F9;
  
  /* Shadows */
  --shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  
  /* Transitions */
  --transition: all 0.2s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: var(--bg-secondary);
  color: var(--text-primary);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
}

/* Dashboard Layout */
.dashboard {
  display: grid;
  grid-template-columns: 280px 1fr;
  min-height: 100vh;
  background: var(--bg-secondary);
}

app-sidebar-recruiter {
  background: var(--bg-primary);
  border-right: 1px solid var(--border);
}

.main-content {
  background: var(--bg-secondary);
  padding: 24px;
  overflow-x: hidden;
}

/* Header */
.dashboard-header {
  background: var(--bg-primary);
  border-bottom: 1px solid var(--border);
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: var(--shadow);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
}

.header-left h1 {
  font-size: 24px;
  font-weight: 600;
  color: var(--primary);
  margin: 0;
}

.header-left p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 4px 0 0 0;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Search */
.search-container {
  display: flex;
  align-items: center;
  background: var(--bg-light);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 8px 12px;
  width: 280px;
  transition: var(--transition);
}

.search-container:focus-within {
  border-color: var(--accent);
  background: var(--bg-primary);
}

.search-icon {
  color: var(--text-muted);
  font-size: 18px;
  margin-right: 8px;
}

.search-bar {
  border: none;
  background: transparent;
  flex: 1;
  font-size: 14px;
  color: var(--text-primary);
  outline: none;
  font-family: inherit;
}

.search-bar::placeholder {
  color: var(--text-muted);
}

/* Buttons */
.notification-button,
.refresh-button,
.export-button {
  width: 36px;
  height: 36px;
  background: var(--bg-light);
  border: 1px solid var(--border);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
}

.notification-button:hover,
.refresh-button:hover,
.export-button:hover {
  background: var(--accent);
  color: var(--text-white);
  border-color: var(--accent);
}

.profile-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: var(--primary);
  color: var(--text-white);
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: var(--transition);
}

.profile-button:hover {
  background: var(--secondary);
}

/* Content */
.dynamic-content {
  margin-bottom: 24px;
}

/* Cards */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: var(--shadow);
  transition: var(--transition);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  background: var(--bg-primary);
  border: 1px solid var(--border);
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  transition: var(--transition);
}

.stat-card:hover {
  border-color: var(--accent);
  box-shadow: var(--shadow-md);
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-change {
  font-size: 12px;
  margin-top: 4px;
}

.stat-change.positive {
  color: #10b981;
}

.stat-change.negative {
  color: #ef4444;
}

.stat-change.neutral {
  color: var(--text-muted);
}

/* Section Headers */
.section-header {
  margin-bottom: 20px;
}

.section-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: var(--primary);
  margin: 0 0 4px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-header h2 mat-icon {
  font-size: 22px;
  color: var(--accent);
}

.section-header p {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
}

/* Lists */
.list-container {
  background: var(--bg-primary);
  border: 1px solid var(--border);
  border-radius: 8px;
  overflow: hidden;
}

.list-item {
  padding: 16px;
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: var(--transition);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:hover {
  background: var(--bg-light);
}

.item-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.item-avatar {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  background: var(--bg-light);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.item-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-details h4 {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 2px 0;
  cursor: pointer;
}

.item-details h4:hover {
  color: var(--accent);
}

.item-details p {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
}
