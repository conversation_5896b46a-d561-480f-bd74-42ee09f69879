<div class="dashboard">
  <app-sidebar-recruiter (viewChange)="onViewChange($event)"></app-sidebar-recruiter>

  <main class="main-content">
    <!-- Ultra Professional Header -->
    <div class="premium-header">
      <div class="header-background">
        <div class="gradient-overlay"></div>
        <div class="pattern-overlay"></div>
      </div>

      <div class="header-content">
        <div class="header-main">
          <div class="brand-section">
            <div class="logo-container">
              <div class="logo-icon">
                <mat-icon>psychology</mat-icon>
              </div>
              <div class="brand-text">
                <h1 class="brand-title">TalentHub Pro</h1>
                <span class="brand-subtitle">Intelligence Artificielle de Recrutement</span>
              </div>
            </div>
          </div>

          <div class="header-center">
            <div class="welcome-section">
              <h2 class="welcome-title">Tableau de Bord Exécutif</h2>
              <p class="welcome-subtitle">{{ getCurrentDate() }} • {{ getCurrentTime() }}</p>
            </div>
          </div>

          <div class="header-actions">
            <div class="search-premium">
              <mat-icon class="search-icon">search</mat-icon>
              <input type="text" class="search-input" placeholder="Recherche intelligente..." (input)="filterDashboard($event)">
              <div class="search-suggestions" *ngIf="showSearchSuggestions">
                <div class="suggestion-item" *ngFor="let suggestion of searchSuggestions">
                  <mat-icon>{{ suggestion.icon }}</mat-icon>
                  <span>{{ suggestion.text }}</span>
                </div>
              </div>
            </div>

            <div class="action-buttons">
              <button class="action-btn notifications" (click)="toggleNotifications()" [class.active]="showNotifications">
                <mat-icon>notifications_active</mat-icon>
                <span class="btn-label">Alertes</span>
                <div class="notification-indicator" *ngIf="notifications.length > 0">{{ notifications.length }}</div>
              </button>

              <button class="action-btn analytics" (click)="openAnalytics()">
                <mat-icon>analytics</mat-icon>
                <span class="btn-label">Analytics</span>
              </button>

              <button class="action-btn export" (click)="exportDashboardData()">
                <mat-icon>file_download</mat-icon>
                <span class="btn-label">Export</span>
              </button>

              <div class="profile-section">
                <button class="profile-btn" (click)="toggleProfileMenu()">
                  <div class="avatar">
                    <img src="assets/images/avatar-placeholder.jpg" alt="Profile" onerror="this.style.display='none'">
                    <mat-icon *ngIf="!hasProfileImage">account_circle</mat-icon>
                  </div>
                  <div class="profile-info">
                    <span class="profile-name">{{ userName || 'Recruiter Pro' }}</span>
                    <span class="profile-role">Directeur RH</span>
                  </div>
                  <mat-icon class="dropdown-icon">keyboard_arrow_down</mat-icon>
                </button>

                <div class="profile-dropdown" *ngIf="showProfileMenu" [@slideDown]>
                  <div class="dropdown-item" (click)="navigateToProfile()">
                    <mat-icon>person</mat-icon>
                    <span>Mon Profil</span>
                  </div>
                  <div class="dropdown-item" (click)="openSettings()">
                    <mat-icon>settings</mat-icon>
                    <span>Paramètres</span>
                  </div>
                  <div class="dropdown-divider"></div>
                  <div class="dropdown-item logout" (click)="logout()">
                    <mat-icon>logout</mat-icon>
                    <span>Déconnexion</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Status Bar -->
        <div class="status-bar">
          <div class="status-item">
            <div class="status-indicator online"></div>
            <span>Système Opérationnel</span>
          </div>
          <div class="status-item">
            <mat-icon>schedule</mat-icon>
            <span>Dernière sync: {{ lastSyncTime }}</span>
          </div>
          <div class="status-item">
            <mat-icon>trending_up</mat-icon>
            <span>Performance: {{ systemPerformance }}%</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Notifications Panel -->
    <div class="notifications-panel" *ngIf="showNotifications" [@slideIn]>
      <div class="notifications-header">
        <h3>
          <mat-icon>notifications_active</mat-icon>
          Notifications
        </h3>
        <button mat-icon-button (click)="toggleNotifications()">
          <mat-icon>close</mat-icon>
        </button>
      </div>
      <div class="notifications-list">
        <div class="notification-item" *ngFor="let notification of notifications; let i = index" [class]="notification.type">
          <div class="notification-icon">
            <mat-icon>{{ notification.icon }}</mat-icon>
          </div>
          <div class="notification-content">
            <h4>{{ notification.title }}</h4>
            <p>{{ notification.message }}</p>
            <span class="notification-time">{{ notification.time | date:'short' }}</span>
          </div>
          <button mat-icon-button (click)="dismissNotification(i)" class="dismiss-btn">
            <mat-icon>close</mat-icon>
          </button>
        </div>
        <div class="empty-notifications" *ngIf="notifications.length === 0">
          <mat-icon>notifications_none</mat-icon>
          <p>Aucune notification</p>
        </div>
      </div>
    </div>

    <!-- Dynamic Content Area -->
    <section class="dynamic-content" [ngSwitch]="currentView">

      <!-- Ultra Elegant Dashboard Overview -->
      <div *ngSwitchDefault class="elegant-dashboard">

        <!-- Hero Metrics Section -->
        <div class="hero-metrics">
          <div class="hero-card primary">
            <div class="hero-background">
              <div class="floating-elements">
                <div class="float-element"></div>
                <div class="float-element"></div>
                <div class="float-element"></div>
              </div>
            </div>
            <div class="hero-content">
              <div class="hero-icon">
                <mat-icon>auto_awesome</mat-icon>
              </div>
              <div class="hero-data">
                <h3 class="hero-title">Intelligence Recrutement</h3>
                <div class="hero-value">{{ aiEfficiency }}%</div>
                <div class="hero-trend">
                  <mat-icon>trending_up</mat-icon>
                  <span>+{{ aiGrowth }}% ce mois</span>
                </div>
              </div>
              <div class="hero-visual">
                <div class="circular-progress">
                  <svg class="progress-ring" width="80" height="80">
                    <circle class="progress-ring-circle" stroke="url(#gradient1)" stroke-width="4" fill="transparent" r="36" cx="40" cy="40"
                            [style.stroke-dasharray]="226.2" [style.stroke-dashoffset]="226.2 - (226.2 * aiEfficiency) / 100"/>
                  </svg>
                  <div class="progress-center">{{ aiEfficiency }}%</div>
                </div>
              </div>
            </div>
          </div>

          <div class="hero-card secondary">
            <div class="hero-background">
              <div class="wave-pattern"></div>
            </div>
            <div class="hero-content">
              <div class="hero-icon">
                <mat-icon>psychology</mat-icon>
              </div>
              <div class="hero-data">
                <h3 class="hero-title">Performance Globale</h3>
                <div class="hero-value">{{ performanceScore }}%</div>
                <div class="hero-trend">
                  <mat-icon>speed</mat-icon>
                  <span>Excellent niveau</span>
                </div>
              </div>
              <div class="hero-visual">
                <div class="performance-bars">
                  <div class="bar" [style.height.%]="performanceScore"></div>
                  <div class="bar" [style.height.%]="performanceScore - 10"></div>
                  <div class="bar" [style.height.%]="performanceScore - 5"></div>
                  <div class="bar" [style.height.%]="performanceScore - 15"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Premium Stats Grid -->
        <div class="premium-stats">
          <div class="stat-card revenue">
            <div class="stat-header">
              <div class="stat-icon-container">
                <mat-icon>account_balance_wallet</mat-icon>
              </div>
              <div class="stat-actions">
                <button class="stat-action" (click)="viewRevenueDetails()">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button class="stat-action" (click)="exportRevenue()">
                  <mat-icon>file_download</mat-icon>
                </button>
              </div>
            </div>
            <div class="stat-body">
              <h4 class="stat-title">Revenus Générés</h4>
              <div class="stat-value">€{{ formatNumber(monthlyRevenue) }}</div>
              <div class="stat-change positive">
                <mat-icon>arrow_upward</mat-icon>
                <span>+{{ revenueGrowth }}% vs mois dernier</span>
              </div>
              <div class="stat-chart">
                <div class="chart-line"></div>
              </div>
            </div>
          </div>

          <div class="stat-card candidates">
            <div class="stat-header">
              <div class="stat-icon-container">
                <mat-icon>people</mat-icon>
              </div>
              <div class="stat-actions">
                <button class="stat-action" (click)="viewCandidates()">
                  <mat-icon>visibility</mat-icon>
                </button>
              </div>
            </div>
            <div class="stat-body">
              <h4 class="stat-title">Candidats Actifs</h4>
              <div class="stat-value">{{ siteStats.totalUsers }}</div>
              <div class="stat-change positive">
                <mat-icon>person_add</mat-icon>
                <span>+{{ siteStats.newUsersThisMonth }} nouveaux</span>
              </div>
              <div class="stat-progress">
                <div class="progress-track">
                  <div class="progress-fill" [style.width.%]="(siteStats.newUsersThisMonth / siteStats.totalUsers) * 100"></div>
                </div>
              </div>
            </div>
          </div>

          <div class="stat-card jobs">
            <div class="stat-header">
              <div class="stat-icon-container">
                <mat-icon>work</mat-icon>
              </div>
              <div class="stat-actions">
                <button class="stat-action" (click)="createJob()">
                  <mat-icon>add</mat-icon>
                </button>
              </div>
            </div>
            <div class="stat-body">
              <h4 class="stat-title">Offres d'Emploi</h4>
              <div class="stat-value">{{ siteStats.totalJobs }}</div>
              <div class="stat-change neutral">
                <mat-icon>work_outline</mat-icon>
                <span>{{ siteStats.activeJobs }} actives</span>
              </div>
              <div class="stat-distribution">
                <div class="distribution-item">
                  <span class="dist-label">Actives</span>
                  <span class="dist-value">{{ siteStats.activeJobs }}</span>
                </div>
                <div class="distribution-item">
                  <span class="dist-label">En attente</span>
                  <span class="dist-value">{{ siteStats.totalJobs - siteStats.activeJobs }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="stat-card satisfaction">
            <div class="stat-header">
              <div class="stat-icon-container">
                <mat-icon>sentiment_very_satisfied</mat-icon>
              </div>
              <div class="stat-actions">
                <button class="stat-action" (click)="viewFeedback()">
                  <mat-icon>feedback</mat-icon>
                </button>
              </div>
            </div>
            <div class="stat-body">
              <h4 class="stat-title">Satisfaction NPS</h4>
              <div class="stat-value">{{ npsScore }}</div>
              <div class="stat-change positive">
                <mat-icon>thumb_up</mat-icon>
                <span>Score excellent</span>
              </div>
              <div class="nps-gauge">
                <div class="gauge-track"></div>
                <div class="gauge-fill" [style.transform]="'rotate(' + (npsScore * 1.8) + 'deg)'"></div>
                <div class="gauge-center">{{ npsScore }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Advanced Analytics Grid -->
        <div class="analytics-grid">
          <!-- Real-time Activity Feed -->
          <div class="analytics-widget activity-feed">
            <div class="widget-header">
              <h3>
                <mat-icon>timeline</mat-icon>
                Activité en Temps Réel
              </h3>
              <div class="widget-controls">
                <button class="control-btn" (click)="refreshActivity()">
                  <mat-icon>refresh</mat-icon>
                </button>
                <button class="control-btn" (click)="toggleActivityFilter()">
                  <mat-icon>filter_list</mat-icon>
                </button>
              </div>
            </div>
            <div class="widget-content">
              <div class="activity-list">
                <div class="activity-item" *ngFor="let activity of realtimeActivities; let i = index"
                     [class]="activity.type" [@fadeInUp]="i">
                  <div class="activity-icon">
                    <mat-icon>{{ activity.icon }}</mat-icon>
                  </div>
                  <div class="activity-details">
                    <span class="activity-text">{{ activity.message }}</span>
                    <span class="activity-time">{{ activity.timestamp | date:'short' }}</span>
                  </div>
                  <div class="activity-status" [class]="activity.status">
                    {{ activity.status }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Advanced KPI Dashboard -->
          <div class="analytics-widget kpi-dashboard">
            <div class="widget-header">
              <h3>
                <mat-icon>dashboard</mat-icon>
                KPIs Avancés
              </h3>
              <div class="widget-controls">
                <select class="period-selector" [(ngModel)]="selectedPeriod" (change)="updateKPIs()">
                  <option value="today">Aujourd'hui</option>
                  <option value="week">Cette semaine</option>
                  <option value="month">Ce mois</option>
                  <option value="quarter">Ce trimestre</option>
                </select>
              </div>
            </div>
            <div class="widget-content">
              <div class="kpi-grid">
                <div class="kpi-item">
                  <div class="kpi-label">Taux de Conversion</div>
                  <div class="kpi-value">{{ conversionRate }}%</div>
                  <div class="kpi-progress">
                    <div class="progress-bar" [style.width.%]="conversionRate"></div>
                  </div>
                </div>
                <div class="kpi-item">
                  <div class="kpi-label">Temps Moyen de Recrutement</div>
                  <div class="kpi-value">{{ avgRecruitmentTime }} jours</div>
                  <div class="kpi-progress">
                    <div class="progress-bar" [style.width.%]="(30 - avgRecruitmentTime) * 3.33"></div>
                  </div>
                </div>
                <div class="kpi-item">
                  <div class="kpi-label">Qualité des Candidats</div>
                  <div class="kpi-value">{{ candidateQuality }}%</div>
                  <div class="kpi-progress">
                    <div class="progress-bar" [style.width.%]="candidateQuality"></div>
                  </div>
                </div>
                <div class="kpi-item">
                  <div class="kpi-label">ROI Recrutement</div>
                  <div class="kpi-value">{{ recruitmentROI }}%</div>
                  <div class="kpi-progress">
                    <div class="progress-bar" [style.width.%]="recruitmentROI"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Workspace Profile View -->
      <div *ngSwitchCase="'workspace'" class="workspace-profile-view">
        <div class="workspace-header" *ngIf="activeWorkspaceProfile">
          <div class="workspace-banner">
            <div class="workspace-cover"></div>
            <div class="workspace-info">
              <div class="workspace-avatar">
                <img [src]="activeWorkspaceProfile.logoUrl || '/assets/default-workspace.png'"
                     [alt]="activeWorkspaceProfile.name"
                     class="workspace-logo">
              </div>
              <div class="workspace-details">
                <h1 class="workspace-name">{{ activeWorkspaceProfile.name }}</h1>
                <p class="workspace-description">{{ activeWorkspaceProfile.description || 'Espace de travail professionnel' }}</p>
                <div class="workspace-meta">
                  <div class="meta-item">
                    <mat-icon>location_on</mat-icon>
                    <span>{{ activeWorkspaceProfile.location || 'Non spécifié' }}</span>
                  </div>
                  <div class="meta-item">
                    <mat-icon>business</mat-icon>
                    <span>{{ activeWorkspaceProfile.industry || 'Technologie' }}</span>
                  </div>
                  <div class="meta-item">
                    <mat-icon>people</mat-icon>
                    <span>{{ activeWorkspaceProfile.employeeCount || '1-50' }} employés</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="workspace-content">
          <div class="workspace-stats-grid">
            <div class="stat-card primary">
              <div class="stat-icon">
                <mat-icon>work</mat-icon>
              </div>
              <div class="stat-content">
                <h3>Offres Actives</h3>
                <div class="stat-value">{{ workspaceStats.activePosts }}</div>
                <div class="stat-trend positive">+{{ workspaceStats.newPostsThisMonth }} ce mois</div>
              </div>
            </div>

            <div class="stat-card success">
              <div class="stat-icon">
                <mat-icon>people</mat-icon>
              </div>
              <div class="stat-content">
                <h3>Candidatures</h3>
                <div class="stat-value">{{ workspaceStats.totalApplications }}</div>
                <div class="stat-trend positive">+{{ workspaceStats.newApplicationsToday }} aujourd'hui</div>
              </div>
            </div>

            <div class="stat-card warning">
              <div class="stat-icon">
                <mat-icon>quiz</mat-icon>
              </div>
              <div class="stat-content">
                <h3>Tests Complétés</h3>
                <div class="stat-value">{{ workspaceStats.completedTests }}</div>
                <div class="stat-trend neutral">{{ workspaceStats.averageScore }}% score moyen</div>
              </div>
            </div>

            <div class="stat-card info">
              <div class="stat-icon">
                <mat-icon>trending_up</mat-icon>
              </div>
              <div class="stat-content">
                <h3>Taux de Conversion</h3>
                <div class="stat-value">{{ workspaceStats.conversionRate }}%</div>
                <div class="stat-trend positive">+{{ workspaceStats.conversionGrowth }}% vs mois dernier</div>
              </div>
            </div>
          </div>

          <div class="workspace-actions">
            <button class="action-btn primary" (click)="editWorkspaceProfile()">
              <mat-icon>edit</mat-icon>
              Modifier le Profil
            </button>
            <button class="action-btn secondary" (click)="viewWorkspaceAnalytics()">
              <mat-icon>analytics</mat-icon>
              Analytics Avancées
            </button>
            <button class="action-btn tertiary" (click)="manageWorkspaceTeam()">
              <mat-icon>group</mat-icon>
              Gérer l'Équipe
            </button>
          </div>
        </div>
      </div>

      <!-- Posts Management View -->
      <div *ngSwitchCase="'posts'" class="posts-management-view">
        <div class="posts-header">
          <div class="header-content">
            <div class="header-left">
              <h2>
                <mat-icon>article</mat-icon>
                Mes Offres d'Emploi
              </h2>
              <p>Gérez vos offres publiées dans {{ activeWorkspaceProfile?.name || 'votre workspace' }}</p>
            </div>
            <div class="header-actions">
              <button class="filter-btn" (click)="togglePostsFilter()">
                <mat-icon>filter_list</mat-icon>
                Filtrer
              </button>
              <button class="create-btn" (click)="createNewPost()">
                <mat-icon>add</mat-icon>
                Nouvelle Offre
              </button>
            </div>
          </div>
        </div>

        <div class="posts-stats-overview">
          <div class="stat-item">
            <div class="stat-icon published">
              <mat-icon>visibility</mat-icon>
            </div>
            <div class="stat-details">
              <span class="stat-label">Offres Publiées</span>
              <span class="stat-value">{{ workspacePosts.length }}</span>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon applications">
              <mat-icon>people</mat-icon>
            </div>
            <div class="stat-details">
              <span class="stat-label">Candidatures Reçues</span>
              <span class="stat-value">{{ getTotalApplications() }}</span>
            </div>
          </div>
          <div class="stat-item">
            <div class="stat-icon views">
              <mat-icon>visibility</mat-icon>
            </div>
            <div class="stat-details">
              <span class="stat-label">Vues Totales</span>
              <span class="stat-value">{{ getTotalViews() }}</span>
            </div>
          </div>
        </div>

        <div class="posts-list" *ngIf="workspacePosts.length > 0; else noPosts">
          <div class="post-card" *ngFor="let post of workspacePosts; trackBy: trackByPostId" [@fadeInUp]>
            <div class="post-header">
              <div class="post-title-section">
                <h3 class="post-title">{{ post.titre }}</h3>
                <span class="post-status" [class]="post.status || 'active'">
                  {{ getPostStatusLabel(post.status) }}
                </span>
              </div>
              <div class="post-actions">
                <button class="action-btn view" (click)="viewPost(post)" title="Voir l'offre">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button class="action-btn edit" (click)="editPost(post)" title="Modifier">
                  <mat-icon>edit</mat-icon>
                </button>
                <button class="action-btn candidates" (click)="viewPostCandidates(post)" title="Voir les candidats">
                  <mat-icon>people</mat-icon>
                </button>
                <button class="action-btn more" (click)="showPostMenu(post)" title="Plus d'options">
                  <mat-icon>more_vert</mat-icon>
                </button>
              </div>
            </div>

            <div class="post-content">
              <p class="post-description">{{ post.description | slice:0:150 }}{{ post.description?.length > 150 ? '...' : '' }}</p>
              <div class="post-meta">
                <div class="meta-item">
                  <mat-icon>business</mat-icon>
                  <span>{{ post.entreprise }}</span>
                </div>
                <div class="meta-item">
                  <mat-icon>work</mat-icon>
                  <span>{{ post.contractType }}</span>
                </div>
                <div class="meta-item">
                  <mat-icon>schedule</mat-icon>
                  <span>{{ post.datePublication | date:'dd/MM/yyyy' }}</span>
                </div>
              </div>
            </div>

            <div class="post-stats">
              <div class="stat">
                <mat-icon>people</mat-icon>
                <span>{{ post.applicationsCount || 0 }} candidatures</span>
              </div>
              <div class="stat">
                <mat-icon>visibility</mat-icon>
                <span>{{ post.viewsCount || 0 }} vues</span>
              </div>
              <div class="stat">
                <mat-icon>schedule</mat-icon>
                <span>{{ getTimeAgo(post.datePublication) }}</span>
              </div>
            </div>
          </div>
        </div>

        <ng-template #noPosts>
          <div class="empty-state">
            <div class="empty-icon">
              <mat-icon>work_off</mat-icon>
            </div>
            <h3>Aucune offre d'emploi</h3>
            <p>Vous n'avez pas encore publié d'offres dans ce workspace.</p>
            <button class="create-first-post-btn" (click)="createNewPost()">
              <mat-icon>add</mat-icon>
              Créer votre première offre
            </button>
          </div>
        </ng-template>
      </div>

      <!-- Candidates Management View -->
      <div *ngSwitchCase="'candidates'" class="candidates-management-view">
        <div class="candidates-header">
          <div class="header-content">
            <div class="header-left">
              <h2>
                <mat-icon>people</mat-icon>
                Candidats du Workspace
              </h2>
              <p>Gérez les candidatures pour vos offres dans {{ activeWorkspaceProfile?.name || 'votre workspace' }}</p>
            </div>
            <div class="header-actions">
              <div class="filter-group">
                <select class="filter-select" [(ngModel)]="candidatesFilter" (change)="filterCandidates()">
                  <option value="all">Tous les candidats</option>
                  <option value="pending">En attente</option>
                  <option value="accepted">Acceptés</option>
                  <option value="rejected">Rejetés</option>
                </select>
              </div>
              <button class="export-btn" (click)="exportCandidates()">
                <mat-icon>download</mat-icon>
                Exporter
              </button>
            </div>
          </div>
        </div>

        <div class="candidates-stats-overview">
          <div class="stat-item total">
            <div class="stat-icon">
              <mat-icon>people</mat-icon>
            </div>
            <div class="stat-details">
              <span class="stat-label">Total Candidats</span>
              <span class="stat-value">{{ workspaceCandidates.length }}</span>
            </div>
          </div>
          <div class="stat-item pending">
            <div class="stat-icon">
              <mat-icon>schedule</mat-icon>
            </div>
            <div class="stat-details">
              <span class="stat-label">En Attente</span>
              <span class="stat-value">{{ getPendingCandidatesCount() }}</span>
            </div>
          </div>
          <div class="stat-item accepted">
            <div class="stat-icon">
              <mat-icon>check_circle</mat-icon>
            </div>
            <div class="stat-details">
              <span class="stat-label">Acceptés</span>
              <span class="stat-value">{{ getAcceptedCandidatesCount() }}</span>
            </div>
          </div>
          <div class="stat-item rejected">
            <div class="stat-icon">
              <mat-icon>cancel</mat-icon>
            </div>
            <div class="stat-details">
              <span class="stat-label">Rejetés</span>
              <span class="stat-value">{{ getRejectedCandidatesCount() }}</span>
            </div>
          </div>
        </div>

        <div class="candidates-list" *ngIf="filteredCandidates.length > 0; else noCandidates">
          <div class="candidate-card" *ngFor="let candidate of filteredCandidates; trackBy: trackByCandidateId" [@fadeInUp]>
            <div class="candidate-header">
              <div class="candidate-avatar" (click)="viewCandidateProfile(candidate)">
                <img [src]="candidate.profileImage || '/assets/default-avatar.png'"
                     [alt]="candidate.name"
                     class="avatar-img">
              </div>
              <div class="candidate-info">
                <h3 class="candidate-name" (click)="viewCandidateProfile(candidate)">{{ candidate.name }}</h3>
                <p class="candidate-email">{{ candidate.email }}</p>
                <div class="candidate-meta">
                  <span class="meta-item">
                    <mat-icon>work</mat-icon>
                    {{ candidate.postTitle }}
                  </span>
                  <span class="meta-item">
                    <mat-icon>schedule</mat-icon>
                    {{ candidate.applicationDate | date:'dd/MM/yyyy' }}
                  </span>
                </div>
              </div>
              <div class="candidate-status">
                <span class="status-badge" [class]="candidate.status || 'pending'">
                  {{ getCandidateStatusLabel(candidate.status) }}
                </span>
              </div>
            </div>

            <div class="candidate-content">
              <div class="candidate-skills" *ngIf="candidate.skills && candidate.skills.length > 0">
                <span class="skill-tag" *ngFor="let skill of candidate.skills.slice(0, 5)">{{ skill }}</span>
                <span class="more-skills" *ngIf="candidate.skills.length > 5">+{{ candidate.skills.length - 5 }}</span>
              </div>

              <div class="candidate-score" *ngIf="candidate.testScore">
                <div class="score-label">Score du test</div>
                <div class="score-value" [class]="getScoreClass(candidate.testScore)">{{ candidate.testScore }}%</div>
              </div>
            </div>

            <div class="candidate-actions">
              <button class="action-btn view" (click)="viewCandidateProfile(candidate)" title="Voir le profil">
                <mat-icon>visibility</mat-icon>
              </button>
              <button class="action-btn cv" (click)="downloadCV(candidate)" title="Télécharger CV">
                <mat-icon>description</mat-icon>
              </button>
              <button class="action-btn message" (click)="sendMessage(candidate)" title="Envoyer un message">
                <mat-icon>message</mat-icon>
              </button>
              <button class="action-btn accept"
                      (click)="acceptCandidate(candidate)"
                      title="Accepter"
                      *ngIf="candidate.status !== 'accepted'">
                <mat-icon>check</mat-icon>
              </button>
              <button class="action-btn reject"
                      (click)="rejectCandidate(candidate)"
                      title="Rejeter"
                      *ngIf="candidate.status !== 'rejected'">
                <mat-icon>close</mat-icon>
              </button>
            </div>
          </div>
        </div>

        <ng-template #noCandidates>
          <div class="empty-state">
            <div class="empty-icon">
              <mat-icon>people_outline</mat-icon>
            </div>
            <h3>Aucun candidat trouvé</h3>
            <p>Aucune candidature n'a été reçue pour vos offres dans ce workspace.</p>
          </div>
        </ng-template>
      </div>

      <!-- Test Results Management View -->
      <div *ngSwitchCase="'testResults'" class="test-results-management-view">
        <div class="test-results-header">
          <div class="header-content">
            <div class="header-left">
              <h2>
                <mat-icon>assessment</mat-icon>
                Résultats des Tests
              </h2>
              <p>Analysez les performances des candidats dans {{ activeWorkspaceProfile?.name || 'votre workspace' }}</p>
            </div>
            <div class="header-actions">
              <div class="filter-group">
                <select class="filter-select" [(ngModel)]="testResultsFilter" (change)="filterTestResults()">
                  <option value="all">Tous les résultats</option>
                  <option value="excellent">Excellents (>80%)</option>
                  <option value="good">Bons (60-80%)</option>
                  <option value="average">Moyens (40-60%)</option>
                  <option value="poor">Faibles (<40%)</option>
                </select>
              </div>
              <button class="analytics-btn" (click)="viewTestAnalytics()">
                <mat-icon>analytics</mat-icon>
                Analytics
              </button>
              <button class="export-btn" (click)="exportTestResults()">
                <mat-icon>download</mat-icon>
                Exporter
              </button>
            </div>
          </div>
        </div>

        <div class="test-stats-overview">
          <div class="stat-item total">
            <div class="stat-icon">
              <mat-icon>quiz</mat-icon>
            </div>
            <div class="stat-details">
              <span class="stat-label">Tests Complétés</span>
              <span class="stat-value">{{ workspaceTestResults.length }}</span>
            </div>
          </div>
          <div class="stat-item average">
            <div class="stat-icon">
              <mat-icon>trending_up</mat-icon>
            </div>
            <div class="stat-details">
              <span class="stat-label">Score Moyen</span>
              <span class="stat-value">{{ getAverageTestScore() }}%</span>
            </div>
          </div>
          <div class="stat-item success">
            <div class="stat-icon">
              <mat-icon>check_circle</mat-icon>
            </div>
            <div class="stat-details">
              <span class="stat-label">Taux de Réussite</span>
              <span class="stat-value">{{ getSuccessRate() }}%</span>
            </div>
          </div>
          <div class="stat-item time">
            <div class="stat-icon">
              <mat-icon>schedule</mat-icon>
            </div>
            <div class="stat-details">
              <span class="stat-label">Temps Moyen</span>
              <span class="stat-value">{{ getAverageTestTime() }}min</span>
            </div>
          </div>
        </div>

        <div class="test-results-list" *ngIf="filteredTestResults.length > 0; else noTestResults">
          <div class="test-result-card" *ngFor="let result of filteredTestResults; trackBy: trackByTestResultId" [@fadeInUp]>
            <div class="result-header">
              <div class="candidate-info">
                <div class="candidate-avatar" (click)="viewCandidateProfile(result.candidate)">
                  <img [src]="result.candidate?.profileImage || '/assets/default-avatar.png'"
                       [alt]="result.candidateName"
                       class="avatar-img">
                </div>
                <div class="candidate-details">
                  <h3 class="candidate-name" (click)="viewCandidateProfile(result.candidate)">{{ result.candidateName }}</h3>
                  <p class="test-info">{{ result.testName }} • {{ result.postTitle }}</p>
                  <span class="test-date">{{ result.completedAt | date:'dd/MM/yyyy HH:mm' }}</span>
                </div>
              </div>
              <div class="score-display">
                <div class="score-circle" [class]="getScoreClass(result.scorePercentage)">
                  <span class="score-value">{{ result.scorePercentage }}%</span>
                </div>
                <div class="score-details">
                  <span class="correct-answers">{{ result.correctAnswers }}/{{ result.totalQuestions }}</span>
                  <span class="score-label">{{ getScoreLabel(result.scorePercentage) }}</span>
                </div>
              </div>
            </div>

            <div class="result-content">
              <div class="test-metrics">
                <div class="metric">
                  <mat-icon>schedule</mat-icon>
                  <span>{{ result.timeSpent || 'N/A' }}</span>
                </div>
                <div class="metric">
                  <mat-icon>speed</mat-icon>
                  <span>{{ result.difficulty || 'Moyen' }}</span>
                </div>
                <div class="metric">
                  <mat-icon>category</mat-icon>
                  <span>{{ result.testCategory || 'Technique' }}</span>
                </div>
              </div>

              <div class="performance-breakdown" *ngIf="result.categoryScores">
                <h4>Performance par Catégorie</h4>
                <div class="category-scores">
                  <div class="category-score" *ngFor="let category of result.categoryScores">
                    <span class="category-name">{{ category.name }}</span>
                    <div class="category-progress">
                      <div class="progress-bar" [style.width.%]="category.score"></div>
                    </div>
                    <span class="category-value">{{ category.score }}%</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="result-actions">
              <button class="action-btn details" (click)="viewTestDetails(result)" title="Voir les détails">
                <mat-icon>visibility</mat-icon>
              </button>
              <button class="action-btn compare" (click)="compareResults(result)" title="Comparer">
                <mat-icon>compare</mat-icon>
              </button>
              <button class="action-btn feedback" (click)="provideFeedback(result)" title="Feedback">
                <mat-icon>feedback</mat-icon>
              </button>
              <button class="action-btn accept"
                      (click)="acceptBasedOnTest(result)"
                      title="Accepter le candidat"
                      *ngIf="result.scorePercentage >= 60">
                <mat-icon>check</mat-icon>
              </button>
              <button class="action-btn schedule" (click)="scheduleInterview(result)" title="Programmer entretien">
                <mat-icon>event</mat-icon>
              </button>
            </div>
          </div>
        </div>

        <ng-template #noTestResults>
          <div class="empty-state">
            <div class="empty-icon">
              <mat-icon>quiz</mat-icon>
            </div>
            <h3>Aucun résultat de test</h3>
            <p>Aucun test n'a été complété pour les offres de ce workspace.</p>
          </div>
        </ng-template>
      </div>
    </section>

    <!-- Performance Metrics Overview -->
    <section class="performance-overview">
      <div class="performance-grid">
        <div class="performance-card excellence">
          <div class="performance-header">
            <mat-icon>trending_up</mat-icon>
            <span>Performance Globale</span>
          </div>
          <div class="performance-value">{{ performanceMetrics.efficiency }}%</div>
          <div class="performance-indicator" [style.width.%]="performanceMetrics.efficiency"></div>
        </div>

        <div class="performance-card growth">
          <div class="performance-header">
            <mat-icon>speed</mat-icon>
            <span>Taux de Croissance</span>
          </div>
          <div class="performance-value">{{ performanceMetrics.growthRate }}%</div>
          <div class="performance-indicator" [style.width.%]="performanceMetrics.growthRate"></div>
        </div>

        <div class="performance-card satisfaction">
          <div class="performance-header">
            <mat-icon>star</mat-icon>
            <span>Satisfaction</span>
          </div>
          <div class="performance-value">{{ performanceMetrics.satisfaction }}%</div>
          <div class="performance-indicator" [style.width.%]="performanceMetrics.satisfaction"></div>
        </div>

        <div class="performance-card response">
          <div class="performance-header">
            <mat-icon>schedule</mat-icon>
            <span>Temps de Réponse</span>
          </div>
          <div class="performance-value">{{ performanceMetrics.responseTime }}h</div>
          <div class="performance-indicator" [style.width.%]="100 - performanceMetrics.responseTime * 4"></div>
        </div>
      </div>
    </section>
