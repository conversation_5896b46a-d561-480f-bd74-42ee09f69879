<div class="dashboard">
  <app-sidebar-recruiter (viewChange)="onViewChange($event)"></app-sidebar-recruiter>

  <main class="main-content">
    <!-- Professional Header -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="dashboard-title">
            <mat-icon class="title-icon">dashboard</mat-icon>
            Dashboard Recruiter
          </h1>
          <p class="dashboard-subtitle">Gestion professionnelle des talents</p>
        </div>
        <div class="header-right">
          <div class="search-container">
            <mat-icon class="search-icon">search</mat-icon>
            <input type="text" class="search-bar" placeholder="Rechercher..." (input)="filterDashboard($event)">
          </div>
          <button class="notification-button" (click)="toggleNotifications()" [class.has-notifications]="notifications.length > 0">
            <mat-icon>notifications</mat-icon>
            <span class="notification-badge" *ngIf="notifications.length > 0">{{ notifications.length }}</span>
          </button>
          <button class="refresh-button" (click)="refreshDashboard()" [class.spinning]="isLoading">
            <mat-icon>refresh</mat-icon>
          </button>
          <button class="export-button" (click)="exportDashboardData()">
            <mat-icon>download</mat-icon>
          </button>
          <button class="profile-button" (click)="navigateToProfile()">
            <mat-icon>account_circle</mat-icon>
            <span>Profil</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Notifications Panel -->
    <div class="notifications-panel" *ngIf="showNotifications" [@slideIn]>
      <div class="notifications-header">
        <h3>
          <mat-icon>notifications_active</mat-icon>
          Notifications
        </h3>
        <button mat-icon-button (click)="toggleNotifications()">
          <mat-icon>close</mat-icon>
        </button>
      </div>
      <div class="notifications-list">
        <div class="notification-item" *ngFor="let notification of notifications; let i = index" [class]="notification.type">
          <div class="notification-icon">
            <mat-icon>{{ notification.icon }}</mat-icon>
          </div>
          <div class="notification-content">
            <h4>{{ notification.title }}</h4>
            <p>{{ notification.message }}</p>
            <span class="notification-time">{{ notification.time | date:'short' }}</span>
          </div>
          <button mat-icon-button (click)="dismissNotification(i)" class="dismiss-btn">
            <mat-icon>close</mat-icon>
          </button>
        </div>
        <div class="empty-notifications" *ngIf="notifications.length === 0">
          <mat-icon>notifications_none</mat-icon>
          <p>Aucune notification</p>
        </div>
      </div>
    </div>

    <!-- Dynamic Content Area -->
    <section class="dynamic-content" [ngSwitch]="currentView">

      <!-- Dashboard Overview (Default) -->
      <div *ngSwitchDefault class="dashboard-overview">
        <div class="overview-header">
          <h2>
            <mat-icon>analytics</mat-icon>
            Vue d'ensemble - Statistiques du Site
          </h2>
          <p>Tableau de bord complet de votre plateforme de recrutement</p>
        </div>

        <!-- Site Statistics Grid -->
        <div class="site-stats-grid">
          <div class="site-stat-card users">
            <div class="stat-icon">
              <mat-icon>people</mat-icon>
            </div>
            <div class="stat-info">
              <h3>Utilisateurs Totaux</h3>
              <div class="stat-value">{{ siteStats.totalUsers }}</div>
              <div class="stat-change positive">+{{ siteStats.newUsersThisMonth }} ce mois</div>
            </div>
          </div>

          <div class="site-stat-card companies">
            <div class="stat-icon">
              <mat-icon>business</mat-icon>
            </div>
            <div class="stat-info">
              <h3>Entreprises Actives</h3>
              <div class="stat-value">{{ siteStats.activeCompanies }}</div>
              <div class="stat-change positive">+{{ siteStats.newCompaniesThisMonth }} nouvelles</div>
            </div>
          </div>

          <div class="site-stat-card jobs">
            <div class="stat-icon">
              <mat-icon>work</mat-icon>
            </div>
            <div class="stat-info">
              <h3>Offres d'Emploi</h3>
              <div class="stat-value">{{ siteStats.totalJobs }}</div>
              <div class="stat-change neutral">{{ siteStats.activeJobs }} actives</div>
            </div>
          </div>

          <div class="site-stat-card applications">
            <div class="stat-icon">
              <mat-icon>assignment</mat-icon>
            </div>
            <div class="stat-info">
              <h3>Candidatures</h3>
              <div class="stat-value">{{ siteStats.totalApplications }}</div>
              <div class="stat-change positive">{{ siteStats.applicationsToday }} aujourd'hui</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Workspace View -->
      <div *ngSwitchCase="'workspace'" class="workspace-view">
        <div class="section-header">
          <h2>
            <mat-icon>business</mat-icon>
            Gestion des Workspaces
          </h2>
          <p>Gérez vos espaces de travail et équipes</p>
        </div>

        <div class="workspace-stats">
          <div class="workspace-card">
            <h3>Workspaces Actifs</h3>
            <div class="big-number">{{ workspaceStats.activeWorkspaces }}</div>
          </div>
          <div class="workspace-card">
            <h3>Membres Totaux</h3>
            <div class="big-number">{{ workspaceStats.totalMembers }}</div>
          </div>
          <div class="workspace-card">
            <h3>Projets en Cours</h3>
            <div class="big-number">{{ workspaceStats.activeProjects }}</div>
          </div>
        </div>
      </div>

      <!-- Posts View -->
      <div *ngSwitchCase="'posts'" class="posts-view">
        <div class="section-header">
          <h2>
            <mat-icon>article</mat-icon>
            Gestion des Offres d'Emploi
          </h2>
          <p>Statistiques et gestion de vos publications</p>
        </div>

        <div class="posts-stats">
          <div class="posts-card">
            <h3>Offres Publiées</h3>
            <div class="big-number">{{ postsStats.totalPosts }}</div>
          </div>
          <div class="posts-card">
            <h3>Vues Totales</h3>
            <div class="big-number">{{ postsStats.totalViews }}</div>
          </div>
          <div class="posts-card">
            <h3>Taux de Conversion</h3>
            <div class="big-number">{{ postsStats.conversionRate }}%</div>
          </div>
        </div>
      </div>

      <!-- Candidates View -->
      <div *ngSwitchCase="'candidates'" class="candidates-view">
        <div class="section-header">
          <h2>
            <mat-icon>people</mat-icon>
            Gestion des Candidats
          </h2>
          <p>Vue d'ensemble de vos candidats et recrutements</p>
        </div>

        <div class="candidates-stats">
          <div class="candidates-card">
            <h3>Candidats Totaux</h3>
            <div class="big-number">{{ candidatesStats.totalCandidates }}</div>
          </div>
          <div class="candidates-card">
            <h3>En Attente</h3>
            <div class="big-number">{{ candidatesStats.pendingCandidates }}</div>
          </div>
          <div class="candidates-card">
            <h3>Acceptés</h3>
            <div class="big-number">{{ candidatesStats.acceptedCandidates }}</div>
          </div>
        </div>
      </div>

      <!-- Test Results View -->
      <div *ngSwitchCase="'testResults'" class="test-results-view">
        <div class="section-header">
          <h2>
            <mat-icon>assessment</mat-icon>
            Résultats des Tests
          </h2>
          <p>Analyse des performances des tests de recrutement</p>
        </div>

        <div class="test-results-stats">
          <div class="test-card">
            <h3>Tests Complétés</h3>
            <div class="big-number">{{ testStats.completedTests }}</div>
          </div>
          <div class="test-card">
            <h3>Score Moyen</h3>
            <div class="big-number">{{ testStats.averageScore }}%</div>
          </div>
          <div class="test-card">
            <h3>Taux de Réussite</h3>
            <div class="big-number">{{ testStats.successRate }}%</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Performance Metrics Overview -->
    <section class="performance-overview">
      <div class="performance-grid">
        <div class="performance-card excellence">
          <div class="performance-header">
            <mat-icon>trending_up</mat-icon>
            <span>Performance Globale</span>
          </div>
          <div class="performance-value">{{ performanceMetrics.efficiency }}%</div>
          <div class="performance-indicator" [style.width.%]="performanceMetrics.efficiency"></div>
        </div>

        <div class="performance-card growth">
          <div class="performance-header">
            <mat-icon>speed</mat-icon>
            <span>Taux de Croissance</span>
          </div>
          <div class="performance-value">{{ performanceMetrics.growthRate }}%</div>
          <div class="performance-indicator" [style.width.%]="performanceMetrics.growthRate"></div>
        </div>

        <div class="performance-card satisfaction">
          <div class="performance-header">
            <mat-icon>star</mat-icon>
            <span>Satisfaction</span>
          </div>
          <div class="performance-value">{{ performanceMetrics.satisfaction }}%</div>
          <div class="performance-indicator" [style.width.%]="performanceMetrics.satisfaction"></div>
        </div>

        <div class="performance-card response">
          <div class="performance-header">
            <mat-icon>schedule</mat-icon>
            <span>Temps de Réponse</span>
          </div>
          <div class="performance-value">{{ performanceMetrics.responseTime }}h</div>
          <div class="performance-indicator" [style.width.%]="100 - performanceMetrics.responseTime * 4"></div>
        </div>
      </div>
    </section>
