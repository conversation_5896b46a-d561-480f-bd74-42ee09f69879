<div class="dashboard">
  <app-sidebar-recruiter (viewChange)="onViewChange($event)"></app-sidebar-recruiter>

  <main class="main-content">
    <!-- Simple Professional Header -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="header-left">
          <h1>Dashboard Recruiter</h1>
          <p>Gérez vos recrutements et analysez vos performances</p>
        </div>
        <div class="header-right">
          <div class="search-container">
            <mat-icon class="search-icon">search</mat-icon>
            <input type="text" class="search-bar" placeholder="Rechercher..." (input)="filterDashboard($event)">
          </div>
          <button class="notification-button">
            <mat-icon>notifications</mat-icon>
          </button>
          <button class="refresh-button">
            <mat-icon>refresh</mat-icon>
          </button>
          <button class="profile-button">
            <mat-icon>account_circle</mat-icon>
            <span>Profil</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Dynamic Content Area -->
    <section class="dynamic-content" [ngSwitch]="currentView">

      <!-- Dashboard Overview (Default) -->
      <div *ngSwitchDefault class="dashboard-overview">
        <h2>Vue d'ensemble</h2>
        
        <!-- Simple Stats Grid -->
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-value">{{ siteStats.totalUsers }}</div>
            <div class="stat-label">Utilisateurs Totaux</div>
            <div class="stat-change positive">+{{ siteStats.newUsersThisMonth }} ce mois</div>
          </div>

          <div class="stat-card">
            <div class="stat-value">{{ siteStats.activeCompanies }}</div>
            <div class="stat-label">Entreprises Actives</div>
            <div class="stat-change positive">+{{ siteStats.newCompaniesThisMonth }} nouvelles</div>
          </div>

          <div class="stat-card">
            <div class="stat-value">{{ siteStats.totalJobs }}</div>
            <div class="stat-label">Offres d'Emploi</div>
            <div class="stat-change neutral">{{ siteStats.activeJobs }} actives</div>
          </div>

          <div class="stat-card">
            <div class="stat-value">{{ siteStats.totalApplications }}</div>
            <div class="stat-label">Candidatures</div>
            <div class="stat-change positive">{{ siteStats.applicationsToday }} aujourd'hui</div>
          </div>
        </div>
      </div>

      <!-- Workspace View -->
      <div *ngSwitchCase="'workspace'" class="workspace-view">
        <div class="section-header">
          <h2>Profil du Workspace</h2>
          <p>Informations et statistiques de votre workspace actif</p>
        </div>

        <div class="card" *ngIf="activeWorkspaceProfile">
          <h3>{{ activeWorkspaceProfile.name || 'Mon Workspace' }}</h3>
          <p>{{ activeWorkspaceProfile.description || 'Description du workspace' }}</p>
          
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-value">{{ workspaceStats.activePosts }}</div>
              <div class="stat-label">Offres Actives</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ workspaceStats.totalApplications }}</div>
              <div class="stat-label">Candidatures</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ workspaceStats.completedTests }}</div>
              <div class="stat-label">Tests Complétés</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Posts View - Using WorkspacePostsComponent -->
      <div *ngSwitchCase="'posts'" class="posts-view">
        <app-workspace-posts></app-workspace-posts>
      </div>

      <!-- Candidates View -->
      <div *ngSwitchCase="'candidates'" class="candidates-view">
        <div class="section-header">
          <h2>Candidats du Workspace</h2>
          <p>Gérez les candidatures pour vos offres</p>
        </div>

        <div class="list-container" *ngIf="filteredCandidates.length > 0; else noCandidates">
          <div class="list-item" *ngFor="let candidate of filteredCandidates">
            <h3 (click)="viewCandidateProfile(candidate)">{{ candidate.name }}</h3>
            <p>{{ candidate.email }} • {{ candidate.postTitle }}</p>
            <div class="action-buttons">
              <button class="btn" (click)="viewCandidateProfile(candidate)">Voir Profil</button>
              <button class="btn btn-accent" (click)="acceptCandidate(candidate)" *ngIf="candidate.status !== 'accepted'">Accepter</button>
              <button class="btn" (click)="rejectCandidate(candidate)" *ngIf="candidate.status !== 'rejected'">Rejeter</button>
            </div>
            <span class="status-badge" [class]="candidate.status">{{ getCandidateStatusLabel(candidate.status) }}</span>
          </div>
        </div>

        <ng-template #noCandidates>
          <div class="empty-state">
            <mat-icon>people_outline</mat-icon>
            <h3>Aucun candidat trouvé</h3>
            <p>Aucune candidature n'a été reçue pour vos offres.</p>
          </div>
        </ng-template>
      </div>

      <!-- Test Results View - Using TestResultsComponent -->
      <div *ngSwitchCase="'testResults'" class="test-results-view">
        <app-testResults></app-testResults>
      </div>

    </section>
  </main>
</div>
