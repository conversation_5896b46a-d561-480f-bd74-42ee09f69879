import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Router } from '@angular/router';
import { JobApplicationService } from '../../services/job-application/job-application.service';
import { WorkspaceService } from '../../services/workspace/workspace.service';
import { PostsService } from '../../services/posts/posts.service';

interface JobApplication {
  id: string;
  name: string;
  email: string;
  phone?: string;
  linkedin?: string;
  introduction?: string;
  postId: string;
  userId: string;
  postTitle: string;
  cvFileName?: string;
  applicationDate: string;
  status: 'PENDING' | 'ACCEPTED' | 'REJECTED';
}

interface Post {
  id: string;
  titre: string;
  description: string;
  entreprise: string;
  userId: string;
  profileRequest: string;
  contractType: string;
  datePublication: string;
  archived: boolean;
  workspace?: {
    id: string;
    name: string;
    logoUrl: string | null;
  };
}

@Component({
  selector: 'app-workspace-candidates',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './workspace-candidates.component.html',
  styleUrl: './workspace-candidates.component.css'
})
export class WorkspaceCandidatesComponent implements OnInit {
  candidates: JobApplication[] = [];
  isLoading = true;
  workspaceName = '';
  activeWorkspaceId: string | null = null;
  totalCandidates = 0;
  pendingCandidates = 0;
  acceptedCandidates = 0;
  rejectedCandidates = 0;

  constructor(
    private jobApplicationService: JobApplicationService,
    private workspaceService: WorkspaceService,
    private postsService: PostsService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadWorkspaceCandidates();
  }

  loadWorkspaceCandidates() {
    this.isLoading = true;
    this.workspaceName = 'Workspace Actif';

    // Simplification: récupérer directement tous les posts
    this.loadAllPostsAndFilter();
  }

  loadAllPostsAndFilter() {
    // Fallback: récupérer tous les posts via PostsService
    this.postsService.getPosts(0, 100).subscribe({
      next: (response: any) => {
        const posts = response.content || response;
        this.loadCandidatesForAllPosts(posts);
      },
      error: (error: any) => {
        console.error('Erreur lors du chargement des posts:', error);
        this.isLoading = false;
      }
    });
  }

  loadCandidatesForAllPosts(posts: Post[]) {
    if (!posts || posts.length === 0) {
      this.candidates = [];
      this.calculateStats();
      this.isLoading = false;
      return;
    }

    const candidatePromises = posts.map(post =>
      this.jobApplicationService.getCandidates(post.id).toPromise().then(applications =>
        applications?.map(application => ({
          ...application,
          postTitle: post.titre, // Utiliser 'titre' au lieu de 'title'
          postId: post.id
        })) || []
      ).catch(error => {
        console.error(`Erreur pour l'offre ${post.id}:`, error);
        return [];
      })
    );

    Promise.all(candidatePromises).then(results => {
      this.candidates = results.flat();
      this.calculateStats();
      this.isLoading = false;
    }).catch(error => {
      console.error('Erreur lors du chargement des candidatures:', error);
      this.isLoading = false;
    });
  }

  calculateStats() {
    this.totalCandidates = this.candidates.length;
    this.pendingCandidates = this.candidates.filter(c => c.status === 'PENDING').length;
    this.acceptedCandidates = this.candidates.filter(c => c.status === 'ACCEPTED').length;
    this.rejectedCandidates = this.candidates.filter(c => c.status === 'REJECTED').length;
  }

  getStatusLabel(status: string): string {
    switch (status) {
      case 'PENDING': return 'En attente';
      case 'ACCEPTED': return 'Accepté';
      case 'REJECTED': return 'Refusé';
      default: return status;
    }
  }

  getStatusClass(status: string): string {
    switch (status) {
      case 'PENDING': return 'status-pending';
      case 'ACCEPTED': return 'status-accepted';
      case 'REJECTED': return 'status-rejected';
      default: return '';
    }
  }

  acceptCandidate(candidateId: string) {
    this.jobApplicationService.acceptApplication(candidateId).subscribe({
      next: (updatedApplication) => {
        const candidate = this.candidates.find(c => c.id === candidateId);
        if (candidate) {
          candidate.status = 'ACCEPTED';
          this.calculateStats();
        }
        console.log('Candidature acceptée avec succès');
      },
      error: (error) => {
        console.error('Erreur lors de l\'acceptation:', error);
      }
    });
  }

  rejectCandidate(candidateId: string) {
    this.jobApplicationService.rejectApplication(candidateId).subscribe({
      next: (updatedApplication) => {
        const candidate = this.candidates.find(c => c.id === candidateId);
        if (candidate) {
          candidate.status = 'REJECTED';
          this.calculateStats();
        }
        console.log('Candidature refusée avec succès');
      },
      error: (error) => {
        console.error('Erreur lors du refus:', error);
      }
    });
  }

  viewCandidateProfile(candidate: JobApplication) {
    // Naviguer vers le profil du candidat en utilisant userId
    if (candidate.userId) {
      this.router.navigate(['/candidate-profile', candidate.userId]);
    }
  }

  viewJobOffer(postId: string, postTitle: string) {
    // Naviguer vers l'offre d'emploi avec le titre
    this.router.navigate(['/post-detail', postId, postTitle]);
  }

  shouldShowActionButtons(candidate: JobApplication): boolean {
    return candidate.status === 'PENDING';
  }
}
