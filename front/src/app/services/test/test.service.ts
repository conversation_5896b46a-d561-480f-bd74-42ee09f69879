import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { switchMap } from 'rxjs/operators';

export interface Test {
  id: string;
  name: string;
  questions: {
    type: 'SELECT' | 'RADIO';
    content: string;
    options: string[];
  }[];
}

export interface TestSubmission {
  testId: string;
  userId: string;
  answers: { questionIndex: number; selectedOptions: number[] }[];
  submittedAt: Date;
}
export interface SubmitResponse {
  message: string;
  score?: string;
  percentage?: string;
}

@Injectable({
  providedIn: 'root',
})
export class TestService {
  private apiUrl = `${environment.apiUrl}/tests`;

  constructor(
    private http: HttpClient,
    private oidcSecurityService: OidcSecurityService
  ) {}

  getTestById(testId: string): Observable<Test> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.get<Test>(`${this.apiUrl}/${testId}`, { headers });
      })
    );
  }
  submitTestAnswers(submission: TestSubmission): Observable<SubmitResponse> {
    return this.http.post<SubmitResponse>(`${this.apiUrl}/${submission.testId}/submit`, submission);
  }
  submitAnswers(id: string, submission: TestSubmission): Observable<any> {
    return this.http.post(`/api/tests/${id}/submit`, submission);
  }



}
