import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import { OidcSecurityService } from 'angular-auth-oidc-client';

@Injectable({
  providedIn: 'root'
})
export class StatisticsService {
  private apiUrl = 'http://localhost:8081/api/statistics';

  constructor(
    private http: HttpClient,
    private oidcSecurityService: OidcSecurityService
  ) {}

  private getAuthHeaders(): Observable<HttpHeaders> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap(token => {
        if (!token) {
          return throwError(() => new Error('No access token available'));
        }
        const headers = new HttpHeaders({
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        });
        return [headers];
      })
    );
  }

  getDashboardStatistics(): Observable<any> {
    return this.getAuthHeaders().pipe(
      switchMap(headers =>
        this.http.get<any>(`${this.apiUrl}/dashboard`, { headers }).pipe(
          catchError(error => {
            console.error('Error fetching dashboard statistics:', error);
            return throwError(() => error);
          })
        )
      )
    );
  }

  getWorkspaceStatistics(workspaceId: string): Observable<any> {
    return this.getAuthHeaders().pipe(
      switchMap(headers =>
        this.http.get<any>(`${this.apiUrl}/workspace/${workspaceId}`, { headers }).pipe(
          catchError(error => {
            console.error('Error fetching workspace statistics:', error);
            return throwError(() => error);
          })
        )
      )
    );
  }

  getSiteStatistics(): Observable<any> {
    return this.getAuthHeaders().pipe(
      switchMap(headers =>
        this.http.get<any>(`${this.apiUrl}/site`, { headers }).pipe(
          catchError(error => {
            console.error('Error fetching site statistics:', error);
            return throwError(() => error);
          })
        )
      )
    );
  }
}
