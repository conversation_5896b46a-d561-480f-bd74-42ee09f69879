# Script PowerShell pour installer Java et démarrer l'application
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Installation de Java et démarrage de l'application" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Fonction pour vérifier si Java est installé
function Test-JavaInstalled {
    try {
        $javaVersion = java -version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Java est déjà installé !" -ForegroundColor Green
            Write-Host $javaVersion[0] -ForegroundColor Yellow
            return $true
        }
    }
    catch {
        return $false
    }
    return $false
}

# Fonction pour chercher Java dans les emplacements communs
function Find-JavaInstallation {
    $javaPaths = @(
        "C:\Program Files\Java\jdk-17\bin\java.exe",
        "C:\Program Files\Java\jdk-11\bin\java.exe",
        "C:\Program Files\Java\jdk-8\bin\java.exe",
        "C:\Program Files (x86)\Java\jdk-17\bin\java.exe",
        "C:\Program Files (x86)\Java\jdk-11\bin\java.exe",
        "C:\Program Files (x86)\Java\jdk-8\bin\java.exe",
        "C:\Program Files\Eclipse Adoptium\jdk-**********-hotspot\bin\java.exe",
        "C:\Program Files\Eclipse Adoptium\jdk-***********-hotspot\bin\java.exe"
    )

    foreach ($javaPath in $javaPaths) {
        if (Test-Path $javaPath) {
            Write-Host "Java trouvé dans : $javaPath" -ForegroundColor Green
            $javaHome = Split-Path (Split-Path $javaPath)
            $env:JAVA_HOME = $javaHome
            $env:PATH = "$javaHome\bin;$env:PATH"
            Write-Host "JAVA_HOME défini à : $javaHome" -ForegroundColor Yellow
            return $true
        }
    }
    return $false
}

# Fonction pour installer Java via Chocolatey
function Install-JavaWithChocolatey {
    Write-Host "Tentative d'installation de Java via Chocolatey..." -ForegroundColor Yellow
    
    # Vérifier si Chocolatey est installé
    try {
        choco --version | Out-Null
        Write-Host "Chocolatey est installé. Installation de Java..." -ForegroundColor Green
        choco install openjdk17 -y
        return $true
    }
    catch {
        Write-Host "Chocolatey n'est pas installé." -ForegroundColor Red
        return $false
    }
}

# Fonction pour installer Chocolatey
function Install-Chocolatey {
    Write-Host "Installation de Chocolatey..." -ForegroundColor Yellow
    try {
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        Invoke-Expression ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
        return $true
    }
    catch {
        Write-Host "Erreur lors de l'installation de Chocolatey : $_" -ForegroundColor Red
        return $false
    }
}

# Script principal
Write-Host "Vérification de l'installation de Java..." -ForegroundColor Cyan

if (Test-JavaInstalled) {
    Write-Host "Java est déjà configuré et fonctionnel." -ForegroundColor Green
}
elseif (Find-JavaInstallation) {
    Write-Host "Java trouvé et configuré avec succès." -ForegroundColor Green
}
else {
    Write-Host "Java n'est pas installé. Tentative d'installation automatique..." -ForegroundColor Yellow
    
    if (-not (Install-JavaWithChocolatey)) {
        Write-Host "Installation de Chocolatey..." -ForegroundColor Yellow
        if (Install-Chocolatey) {
            Write-Host "Chocolatey installé. Redémarrage requis pour installer Java." -ForegroundColor Yellow
            Write-Host "Veuillez redémarrer PowerShell en tant qu'administrateur et relancer ce script." -ForegroundColor Red
            exit 1
        }
        else {
            Write-Host "========================================" -ForegroundColor Red
            Write-Host "ERREUR: Impossible d'installer Java automatiquement" -ForegroundColor Red
            Write-Host "========================================" -ForegroundColor Red
            Write-Host ""
            Write-Host "Veuillez installer Java manuellement :" -ForegroundColor Yellow
            Write-Host "1. Téléchargez Java JDK 17+ depuis : https://adoptium.net/temurin/releases/" -ForegroundColor Cyan
            Write-Host "2. Installez Java et redémarrez PowerShell" -ForegroundColor Cyan
            Write-Host "3. Relancez ce script" -ForegroundColor Cyan
            Write-Host ""
            pause
            exit 1
        }
    }
}

# Démarrer l'application
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Démarrage de l'application Spring Boot" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

Write-Host "Compilation de l'application..." -ForegroundColor Yellow
& .\gradlew.bat build -x test

if ($LASTEXITCODE -ne 0) {
    Write-Host "ERREUR: Échec de la compilation" -ForegroundColor Red
    pause
    exit 1
}

Write-Host "Démarrage du serveur backend..." -ForegroundColor Green
Write-Host "L'application sera disponible sur : http://localhost:8080" -ForegroundColor Cyan
Write-Host "Frontend Angular disponible sur : http://localhost:4201" -ForegroundColor Cyan
Write-Host ""
Write-Host "Appuyez sur Ctrl+C pour arrêter l'application" -ForegroundColor Yellow
Write-Host ""

& .\gradlew.bat bootRun
