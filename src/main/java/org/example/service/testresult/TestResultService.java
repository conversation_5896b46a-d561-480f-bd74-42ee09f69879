package org.example.service.testresult;

import org.example.model.Question;
import org.example.model.Test;
import org.example.model.TestResult;
import org.example.model.User;
import org.example.service.test.TestService;
import org.example.service.user.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class TestResultService {

    @Autowired
    private TestResultRepository testResultRepository;

    @Autowired
    private TestService testService;

    @Autowired
    private UserService userService;

    /**
     * Récupère tous les résultats de test avec les scores calculés
     */
    public List<TestResultWithScore> getAllTestResultsWithScores() {
        List<TestResult> testResults = testResultRepository.findAll();
        List<TestResultWithScore> resultsWithScores = new ArrayList<>();

        for (TestResult testResult : testResults) {
            TestResultWithScore resultWithScore;

            // Si le score est déjà calculé et stocké, l'utiliser
            if (testResult.getTotalQuestions() > 0) {
                resultWithScore = createFromStoredScore(testResult);
            } else {
                // Sinon, calculer le score (pour les anciens résultats)
                resultWithScore = calculateScore(testResult);
            }

            if (resultWithScore != null) {
                resultsWithScores.add(resultWithScore);
            }
        }

        return resultsWithScores;
    }

    /**
     * Crée un TestResultWithScore à partir des données stockées
     */
    private TestResultWithScore createFromStoredScore(TestResult testResult) {
        try {
            // Récupérer le test original pour le nom
            Optional<Test> testOptional = testService.getTestById(testResult.getTestId());
            String testName = testOptional.map(Test::getName).orElse("Test inconnu");

            // Récupérer les informations de l'utilisateur
            Optional<User> userOptional = userService.getUserById(testResult.getUserId());
            String candidateName = userOptional.map(user -> user.getFirstName() + " " + user.getLastName())
                    .orElse("Candidat inconnu");
            String candidateEmail = userOptional.map(User::getEmail).orElse(null);

            // Créer l'objet résultat avec les scores stockés
            TestResultWithScore resultWithScore = new TestResultWithScore();
            resultWithScore.setId(testResult.getId());
            resultWithScore.setTestId(testResult.getTestId());
            resultWithScore.setUserId(testResult.getUserId());
            resultWithScore.setCandidateName(candidateName);
            resultWithScore.setCandidateEmail(candidateEmail);
            resultWithScore.setTestName(testName);
            resultWithScore.setSubmittedAt(testResult.getSubmittedAt());
            resultWithScore.setTotalQuestions(testResult.getTotalQuestions());
            resultWithScore.setCorrectAnswers(testResult.getCorrectAnswers());
            resultWithScore.setScorePercentage(testResult.getScorePercentage());

            return resultWithScore;

        } catch (Exception e) {
            System.err.println("Erreur lors de la création du résultat depuis les données stockées " + testResult.getId() + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * Calcule le score pour un résultat de test donné
     */
    public TestResultWithScore calculateScore(TestResult testResult) {
        try {
            // Récupérer le test original pour avoir les bonnes réponses
            Optional<Test> testOptional = testService.getTestById(testResult.getTestId());
            if (testOptional.isEmpty()) {
                return null;
            }

            Test test = testOptional.get();
            List<Question> questions = test.getQuestions();

            // Récupérer les informations de l'utilisateur
            Optional<User> userOptional = userService.getUserById(testResult.getUserId());
            String candidateName = userOptional.map(user -> user.getFirstName() + " " + user.getLastName())
                    .orElse("Candidat inconnu");
            String candidateEmail = userOptional.map(User::getEmail).orElse(null);

            // Calculer le score
            int totalQuestions = questions.size();
            int correctAnswers = 0;

            for (TestResult.Answer answer : testResult.getAnswers()) {
                int questionIndex = answer.getQuestionIndex();

                // Vérifier que l'index est valide
                if (questionIndex >= 0 && questionIndex < questions.size()) {
                    Question question = questions.get(questionIndex);
                    List<Integer> correctOptions = question.getCorrectOptions();
                    List<Integer> selectedOptions = answer.getSelectedOptions();

                    // Comparer les réponses
                    if (correctOptions != null && selectedOptions != null) {
                        // Trier les listes pour la comparaison
                        correctOptions.sort(Integer::compareTo);
                        selectedOptions.sort(Integer::compareTo);

                        if (correctOptions.equals(selectedOptions)) {
                            correctAnswers++;
                        }
                    }
                }
            }

            // Calculer le pourcentage
            double scorePercentage = totalQuestions > 0 ? (double) correctAnswers / totalQuestions * 100 : 0;

            // Créer l'objet résultat avec score
            TestResultWithScore resultWithScore = new TestResultWithScore();
            resultWithScore.setId(testResult.getId());
            resultWithScore.setTestId(testResult.getTestId());
            resultWithScore.setUserId(testResult.getUserId());
            resultWithScore.setCandidateName(candidateName);
            resultWithScore.setCandidateEmail(candidateEmail);
            resultWithScore.setTestName(test.getName());
            resultWithScore.setSubmittedAt(testResult.getSubmittedAt());
            resultWithScore.setTotalQuestions(totalQuestions);
            resultWithScore.setCorrectAnswers(correctAnswers);
            resultWithScore.setScorePercentage(Math.round(scorePercentage * 100.0) / 100.0); // Arrondir à 2 décimales

            return resultWithScore;

        } catch (Exception e) {
            System.err.println("Erreur lors du calcul du score pour le test result " + testResult.getId() + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * Récupère un résultat de test spécifique avec son score
     */
    public TestResultWithScore getTestResultWithScore(String testResultId) {
        Optional<TestResult> testResultOptional = testResultRepository.findById(testResultId);
        if (testResultOptional.isEmpty()) {
            return null;
        }

        return calculateScore(testResultOptional.get());
    }

    /**
     * Classe interne pour représenter un résultat de test avec score calculé
     */
    public static class TestResultWithScore {
        private String id;
        private String testId;
        private String userId;
        private String candidateName;
        private String candidateEmail;
        private String testName;
        private java.util.Date submittedAt;
        private int totalQuestions;
        private int correctAnswers;
        private double scorePercentage;

        // Getters et Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }

        public String getTestId() { return testId; }
        public void setTestId(String testId) { this.testId = testId; }

        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }

        public String getCandidateName() { return candidateName; }
        public void setCandidateName(String candidateName) { this.candidateName = candidateName; }

        public String getCandidateEmail() { return candidateEmail; }
        public void setCandidateEmail(String candidateEmail) { this.candidateEmail = candidateEmail; }

        public String getTestName() { return testName; }
        public void setTestName(String testName) { this.testName = testName; }

        public java.util.Date getSubmittedAt() { return submittedAt; }
        public void setSubmittedAt(java.util.Date submittedAt) { this.submittedAt = submittedAt; }

        public int getTotalQuestions() { return totalQuestions; }
        public void setTotalQuestions(int totalQuestions) { this.totalQuestions = totalQuestions; }

        public int getCorrectAnswers() { return correctAnswers; }
        public void setCorrectAnswers(int correctAnswers) { this.correctAnswers = correctAnswers; }

        public double getScorePercentage() { return scorePercentage; }
        public void setScorePercentage(double scorePercentage) { this.scorePercentage = scorePercentage; }

        public Object getStatus() {
            return null;
        }
    }
}
