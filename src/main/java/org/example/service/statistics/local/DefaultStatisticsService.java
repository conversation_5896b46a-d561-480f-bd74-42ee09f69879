package org.example.service.statistics.local;

import org.example.service.JobApplication.JobApplicationRepository;
import org.example.service.post.local.PostRepository;
import org.example.service.user.local.UserRepository;
import org.example.service.workspace.local.WorkspaceRepository;
import org.example.service.statistics.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;

@Service
public class DefaultStatisticsService implements StatisticsService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private WorkspaceRepository workspaceRepository;

    @Autowired
    private PostRepository postRepository;

    @Autowired
    private JobApplicationRepository jobApplicationRepository;

    @Override
    public Map<String, Object> getDashboardStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        // Statistiques générales
        long totalUsers = userRepository.count();
        long totalWorkspaces = workspaceRepository.count();
        long totalPosts = postRepository.count();
        long totalApplications = jobApplicationRepository.count();
        
        // Statistiques du mois en cours
        LocalDateTime startOfMonth = LocalDateTime.now().withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS);
        long newUsersThisMonth = userRepository.countByCreatedAtAfter(startOfMonth);
        long newWorkspacesThisMonth = workspaceRepository.countByCreatedAtAfter(startOfMonth);
        long newPostsThisMonth = postRepository.countByDatePublicationAfter(startOfMonth);
        
        // Statistiques du jour
        LocalDateTime startOfDay = LocalDateTime.now().truncatedTo(ChronoUnit.DAYS);
        long applicationsToday = jobApplicationRepository.countByApplicationDateAfter(startOfDay);
        
        // Posts actifs (publiés dans les 30 derniers jours)
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minus(30, ChronoUnit.DAYS);
        long activePosts = postRepository.countByDatePublicationAfter(thirtyDaysAgo);
        
        stats.put("totalUsers", totalUsers);
        stats.put("newUsersThisMonth", newUsersThisMonth);
        stats.put("activeCompanies", totalWorkspaces); // Considérant les workspaces comme entreprises
        stats.put("newCompaniesThisMonth", newWorkspacesThisMonth);
        stats.put("totalJobs", totalPosts);
        stats.put("activeJobs", activePosts);
        stats.put("totalApplications", totalApplications);
        stats.put("applicationsToday", applicationsToday);
        stats.put("newPostsThisMonth", newPostsThisMonth);
        
        return stats;
    }

    @Override
    public Map<String, Object> getWorkspaceStatistics(String workspaceId) {
        Map<String, Object> stats = new HashMap<>();
        
        // Statistiques du workspace spécifique
        long workspacePosts = postRepository.countByWorkspaceId(workspaceId);
        long workspaceApplications = jobApplicationRepository.countByWorkspaceId(workspaceId);
        long workspaceMembers = userRepository.countByWorkspaceId(workspaceId);
        
        // Statistiques du mois en cours pour ce workspace
        LocalDateTime startOfMonth = LocalDateTime.now().withDayOfMonth(1).truncatedTo(ChronoUnit.DAYS);
        long newPostsThisMonth = postRepository.countByWorkspaceIdAndDatePublicationAfter(workspaceId, startOfMonth);
        
        // Statistiques du jour pour ce workspace
        LocalDateTime startOfDay = LocalDateTime.now().truncatedTo(ChronoUnit.DAYS);
        long newApplicationsToday = jobApplicationRepository.countByWorkspaceIdAndApplicationDateAfter(workspaceId, startOfDay);
        
        // Posts actifs du workspace
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minus(30, ChronoUnit.DAYS);
        long activePosts = postRepository.countByWorkspaceIdAndDatePublicationAfter(workspaceId, thirtyDaysAgo);
        
        stats.put("activePosts", activePosts);
        stats.put("totalApplications", workspaceApplications);
        stats.put("totalMembers", workspaceMembers);
        stats.put("newPostsThisMonth", newPostsThisMonth);
        stats.put("newApplicationsToday", newApplicationsToday);
        stats.put("totalPosts", workspacePosts);
        
        return stats;
    }

    @Override
    public Map<String, Object> getSiteStatistics() {
        return getDashboardStatistics(); // Même chose que les statistiques du dashboard
    }
}
