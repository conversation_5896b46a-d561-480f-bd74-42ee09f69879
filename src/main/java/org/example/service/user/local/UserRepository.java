package org.example.service.user.local;

import org.example.model.User;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface UserRepository extends MongoRepository<User, String> {

    @Query("{email: {$regex: ?0, $options: 'i'}}")
    Optional<User> findByEmail(String email);

    @Query(value = "{email: {$regex: ?0, $options: 'i'}}", exists = true)
    boolean existsByEmail(String email);

    @Query(value = "{$and: [{$or: [{email: {$regex: ?0, $options: 'i'}}, {firstName: {$regex: ?0, $options: 'i'}}, {lastName: {$regex: ?0, $options: 'i'}}]}, {_id: {$nin: ?1}}]}")
    List<User> searchForInvitation(String search, Set<String> excludedIds);

    @Query(value = "{_id: {$nin: ?0}}")
    List<User> searchForInvitation(Set<String> excludedIds);

    List<User> findByWorkspaceId(String workspaceId);

    List<User> findCandidatesByPostId(String postId);

    // Méthodes pour les statistiques
    long countByCreatedAtAfter(LocalDateTime date);

    long countByWorkspaceId(String workspaceId);
}
