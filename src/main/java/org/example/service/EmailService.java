package org.example.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

@Service
public class EmailService {

    @Autowired
    private JavaMailSender mailSender;

    /**
     * Envoie un email d'acceptation au candidat
     */
    public void sendAcceptanceEmail(String candidateEmail, String candidateName, String jobTitle) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(candidateEmail);
            message.setSubject("🎉 Félicitations ! Votre candidature a été acceptée");
            
            String emailBody = String.format(
                "Bonjour %s,\n\n" +
                "Nous avons le plaisir de vous informer que votre candidature pour le poste de %s a été acceptée !\n\n" +
                "Votre performance au test technique a été excellente et nous souhaitons poursuivre le processus de recrutement avec vous.\n\n" +
                "Prochaines étapes :\n" +
                "• Un membre de notre équipe RH vous contactera dans les 48h\n" +
                "• Nous organiserons un entretien technique approfondi\n" +
                "• Présentation de l'équipe et de l'environnement de travail\n\n" +
                "Nous sommes impatients de vous rencontrer !\n\n" +
                "Cordialement,\n" +
                "L'équipe de recrutement",
                candidateName, jobTitle
            );
            
            message.setText(emailBody);
            message.setFrom("<EMAIL>");
            
            mailSender.send(message);
            System.out.println("✅ Email d'acceptation envoyé à : " + candidateEmail);
            
        } catch (Exception e) {
            System.err.println("❌ Erreur lors de l'envoi de l'email d'acceptation : " + e.getMessage());
            throw new RuntimeException("Erreur lors de l'envoi de l'email d'acceptation", e);
        }
    }

    /**
     * Envoie un email de refus au candidat
     */
    public void sendRejectionEmail(String candidateEmail, String candidateName, String jobTitle) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(candidateEmail);
            message.setSubject("Mise à jour concernant votre candidature");
            
            String emailBody = String.format(
                "Bonjour %s,\n\n" +
                "Nous vous remercions pour l'intérêt que vous avez porté au poste de %s et pour le temps que vous avez consacré à notre processus de recrutement.\n\n" +
                "Après avoir examiné attentivement votre candidature et vos résultats au test technique, nous avons décidé de ne pas donner suite à votre candidature pour ce poste spécifique.\n\n" +
                "Cette décision ne reflète en rien vos compétences professionnelles. Le processus de sélection était très compétitif et nous avons dû faire des choix difficiles.\n\n" +
                "Nous vous encourageons à postuler pour d'autres opportunités qui pourraient correspondre à votre profil dans le futur.\n\n" +
                "Nous vous souhaitons beaucoup de succès dans vos recherches professionnelles.\n\n" +
                "Cordialement,\n" +
                "L'équipe de recrutement",
                candidateName, jobTitle
            );
            
            message.setText(emailBody);
            message.setFrom("<EMAIL>");
            
            mailSender.send(message);
            System.out.println("✅ Email de refus envoyé à : " + candidateEmail);
            
        } catch (Exception e) {
            System.err.println("❌ Erreur lors de l'envoi de l'email de refus : " + e.getMessage());
            throw new RuntimeException("Erreur lors de l'envoi de l'email de refus", e);
        }
    }

    /**
     * Envoie un email de notification générique
     */
    public void sendNotificationEmail(String to, String subject, String body) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(to);
            message.setSubject(subject);
            message.setText(body);
            message.setFrom("<EMAIL>");
            
            mailSender.send(message);
            System.out.println("✅ Email de notification envoyé à : " + to);
            
        } catch (Exception e) {
            System.err.println("❌ Erreur lors de l'envoi de l'email de notification : " + e.getMessage());
            throw new RuntimeException("Erreur lors de l'envoi de l'email de notification", e);
        }
    }
}
