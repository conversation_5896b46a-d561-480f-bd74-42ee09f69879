package org.example.service.JobApplication;

import org.example.model.JobApplication;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

public interface JobApplicationRepository extends MongoRepository<JobApplication, String> {

    @Query("{ 'postId': ?0 }")
    List<JobApplication> findByPostId(String postId);

    @Query("{ 'postId': ?0, 'userId': ?1 }")
    boolean existsByPostIdAndUserId(String postId, String userId);

    @Query("{ 'userId': ?0 }")
    List<JobApplication> findByUserId(String userId);

    // Méthodes pour les statistiques
    long countByApplicationDateAfter(LocalDateTime date);

    long countByWorkspaceId(String workspaceId);

    long countByWorkspaceIdAndApplicationDateAfter(String workspaceId, LocalDateTime date);
}
