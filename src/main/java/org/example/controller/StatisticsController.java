package org.example.controller;

import org.example.service.statistics.StatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/statistics")
@CrossOrigin(origins = "http://localhost:4200")
public class StatisticsController {

    @Autowired
    private StatisticsService statisticsService;

    @GetMapping("/dashboard")
    public ResponseEntity<Map<String, Object>> getDashboardStatistics() {
        Map<String, Object> stats = statisticsService.getDashboardStatistics();
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/workspace/{workspaceId}")
    public ResponseEntity<Map<String, Object>> getWorkspaceStatistics(@PathVariable String workspaceId) {
        Map<String, Object> stats = statisticsService.getWorkspaceStatistics(workspaceId);
        return ResponseEntity.ok(stats);
    }

    @GetMapping("/site")
    public ResponseEntity<Map<String, Object>> getSiteStatistics() {
        Map<String, Object> stats = statisticsService.getSiteStatistics();
        return ResponseEntity.ok(stats);
    }
}
