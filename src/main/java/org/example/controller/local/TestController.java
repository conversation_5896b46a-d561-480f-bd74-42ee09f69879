package org.example.controller.local;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.dto.TestDTO;
import org.example.model.Test;
import org.example.model.TestResult;
import org.example.model.UserProfile;
import org.example.service.modelIA.LlamaService;
import org.example.service.test.TestService;
import org.example.service.testresult.TestResultRepository;
import org.example.service.testresult.TestResultService;
import org.example.service.userProfile.UserProfileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestMethod;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RestController
@RequestMapping("/api/tests")
@CrossOrigin(origins = "http://localhost:4200", allowedHeaders = {"Authorization", "Content-Type"}, methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.PATCH, RequestMethod.DELETE, RequestMethod.OPTIONS}, allowCredentials = "true")
public class TestController {

    private final Pattern CLEANING_PATTERN = Pattern.compile("```json([?:.|\\n\\s\\S]*)```");

    private final TestService testService;
    private final LlamaService llamaService;
    private final ObjectMapper objectMapper;
    @Autowired
    private TestResultRepository testResultRepository;

    @Autowired
    private TestResultService testResultService;

    @Autowired
    private UserProfileService userProfileService;

    public TestController(TestService testService, LlamaService llamaService, ObjectMapper objectMapper) {
        this.testService = testService;
        this.llamaService = llamaService;
        this.objectMapper = objectMapper;
    }

    @PostMapping
    public Mono<Test> createTest(@RequestBody TestDTO testDTO) {
        return Mono.just(testService.createTest(testDTO));
    }

    @GetMapping
    public Mono<List<Test>> getAllTests() {
        return Mono.just(testService.getAllTests());
    }

    @GetMapping("/{id}")
    public Mono<Test> getTestById(@PathVariable String id) {
        return testService.getTestById(id)
                .map(Mono::just)
                .orElseGet(() -> Mono.error(new RuntimeException("Test not found with id: " + id)));
    }

    @PutMapping("/{id}")
    public Mono<Test> updateTest(@PathVariable String id, @RequestBody TestDTO testDTO) {
        return Mono.just(testService.updateTest(id, testDTO));
    }

    @DeleteMapping("/{id}")
    public Mono<Void> deleteTest(@PathVariable String id) {
        testService.deleteTest(id);
        return Mono.empty();
    }

    @PostMapping("/generate")
    public Mono<Test> generateTest(@RequestBody String skill) {
        if (skill == null || skill.trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("Skill cannot be empty"));
        }

        String prompt = """
                Génère un test technique sur le thème: %s
                contenant 10 questions à selection simple ou multiple.
                Je veux le résultat en json sous la forme:
                 {"title": "Titre du test", "questions":
                 [{
                 "content": "THE QUESTION",
                 "type": "RADIO|SELECT",
                 options: ["Option 1", "Option 2"],
                 correctOptions: [0]}]
                 }
                 type: SELECT pour selection multiple RADIO pour selection simple
                 options: les choix minimum 4
                 correctOptions: les indices des choix correctes
                """.formatted(skill);

        return llamaService.askQuestion(prompt)
                .map(json -> {
                    try {
                        // Log raw JSON for debugging
                        System.out.println("Raw JSON from LlamaService: " + json);
                        Matcher matcher = CLEANING_PATTERN.matcher(json);
                        if (matcher.find()) {
                            String cleanedJson = matcher.group(1).trim();
                            return objectMapper.readValue(cleanedJson, TestDTO.class);
                        }
                        throw new JsonParseException(json);
                    } catch (JsonParseException e) {
                        throw new RuntimeException("Invalid JSON format received from LlamaService: " + e.getMessage(), e);
                    } catch (Exception e) {
                        throw new RuntimeException("Error during JSON conversion to TestDTO: " + e.getMessage(), e);
                    }
                })
                .map(testService::createTest)
                .onErrorResume(e -> {
                    if (e.getCause() instanceof org.apache.catalina.connector.ClientAbortException) {
                        System.out.println("Client disconnected: " + e.getMessage());
                        return Mono.empty();
                    }
                    return Mono.error(e);
                });
    }

    @PostMapping("/generate-custom")
    public Mono<Test> generateCustomTest(@RequestBody Map<String, Object> testParameters) {
        try {
            // Extraire les paramètres
            String level = (String) testParameters.get("level");
            Integer questionCount = (Integer) testParameters.get("questionCount");
            String testType = (String) testParameters.get("testType");
            Integer duration = (Integer) testParameters.get("duration");
            String candidateId = (String) testParameters.get("candidateId");
            String skill = (String) testParameters.getOrDefault("skill", "programmation");

            // Validation des paramètres
            if (level == null || questionCount == null || testType == null || duration == null) {
                return Mono.error(new IllegalArgumentException("Paramètres manquants: level, questionCount, testType, duration sont requis"));
            }

            // Construire le prompt personnalisé
            String prompt = buildCustomPrompt(skill, level, questionCount, testType, duration);

            System.out.println("🤖 Génération de test personnalisé:");
            System.out.println("   - Niveau: " + level);
            System.out.println("   - Questions: " + questionCount);
            System.out.println("   - Type: " + testType);
            System.out.println("   - Durée: " + duration + " minutes");
            System.out.println("   - Candidat: " + candidateId);

            return llamaService.askQuestion(prompt)
                    .map(json -> {
                        try {
                            System.out.println("Raw JSON from LlamaService: " + json);
                            Matcher matcher = CLEANING_PATTERN.matcher(json);
                            if (matcher.find()) {
                                String cleanedJson = matcher.group(1).trim();
                                return objectMapper.readValue(cleanedJson, TestDTO.class);
                            }
                            throw new JsonParseException(json);
                        } catch (JsonParseException e) {
                            throw new RuntimeException("Invalid JSON format received from LlamaService: " + e.getMessage(), e);
                        } catch (Exception e) {
                            throw new RuntimeException("Error during JSON conversion to TestDTO: " + e.getMessage(), e);
                        }
                    })
                    .map(testService::createTest)
                    .onErrorResume(e -> {
                        if (e.getCause() instanceof org.apache.catalina.connector.ClientAbortException) {
                            System.out.println("Client disconnected: " + e.getMessage());
                            return Mono.empty();
                        }
                        return Mono.error(e);
                    });

        } catch (Exception e) {
            return Mono.error(new RuntimeException("Erreur lors de la génération du test personnalisé: " + e.getMessage(), e));
        }
    }

    @PostMapping("/{id}/submit")
    public Mono<Map<String, String>> submitTest(
            @PathVariable String id,
            @RequestBody TestResult testResult) {
        testResult.setTestId(id);
        testResult.setSubmittedAt(new Date());

        // Calculer le score automatiquement lors de la soumission
        return Mono.fromCallable(() -> {
            try {
                // Calculer le score
                TestResultService.TestResultWithScore scoreResult = testResultService.calculateScore(testResult);

                if (scoreResult != null) {
                    // Mettre à jour le testResult avec le score calculé
                    testResult.setTotalQuestions(scoreResult.getTotalQuestions());
                    testResult.setCorrectAnswers(scoreResult.getCorrectAnswers());
                    testResult.setScorePercentage(scoreResult.getScorePercentage());

                    System.out.println("✅ Score calculé pour le test " + id + ": " +
                        scoreResult.getCorrectAnswers() + "/" + scoreResult.getTotalQuestions() +
                        " (" + Math.round(scoreResult.getScorePercentage()) + "%)");
                } else {
                    System.out.println("⚠️ Impossible de calculer le score pour le test " + id);
                    // Valeurs par défaut si le calcul échoue
                    testResult.setTotalQuestions(0);
                    testResult.setCorrectAnswers(0);
                    testResult.setScorePercentage(0.0);
                }

                // Sauvegarder le résultat
                TestResult savedResult = testResultRepository.save(testResult);

                // Retourner une réponse de confirmation
                Map<String, String> response = new HashMap<>();
                response.put("message", "Test result submitted successfully");
                response.put("resultId", savedResult.getId());
                response.put("score", Math.round(savedResult.getScorePercentage()) + "%");

                return response;

            } catch (Exception e) {
                System.err.println("❌ Erreur lors de la soumission du résultat: " + e.getMessage());
                throw new RuntimeException("Erreur lors de la soumission du résultat de test");
            }
        });
    }

    /**
     * Accepte un candidat et envoie un email de notification
     */
    @PostMapping("/results/{resultId}/status")
    public Mono<Map<String, Object>> updateCandidateStatus(
            @PathVariable String resultId,
            @RequestBody Map<String, Object> request) {

        return Mono.fromCallable(() -> {
            try {
                String action = (String) request.get("action");
                String candidateEmail = (String) request.get("candidateEmail");

                System.out.println("🔄 Mise à jour du statut du candidat - Action: " + action + ", Email: " + candidateEmail);

                // Récupérer le résultat de test
                Optional<TestResult> testResultOpt = testResultRepository.findById(resultId);
                if (!testResultOpt.isPresent()) {
                    throw new RuntimeException("Résultat de test non trouvé");
                }

                TestResult testResult = testResultOpt.get();

                // Mettre à jour le statut
                if ("accept".equals(action)) {
                    testResult.setStatus("accepted");
                    System.out.println("✅ Candidat accepté: " + testResult.getCandidateName());

                    // Envoyer l'email d'acceptation
                    sendAcceptanceEmail(candidateEmail, testResult.getCandidateName());

                } else if ("reject".equals(action)) {
                    testResult.setStatus("rejected");
                    System.out.println("❌ Candidat refusé: " + testResult.getCandidateName());

                    // Envoyer l'email de refus
                    sendRejectionEmail(candidateEmail, testResult.getCandidateName());
                }

                // Sauvegarder les modifications
                testResultRepository.save(testResult);

                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("message", "Statut mis à jour avec succès");
                response.put("status", testResult.getStatus());
                response.put("emailSent", true);

                return response;

            } catch (Exception e) {
                System.err.println("❌ Erreur lors de la mise à jour du statut: " + e.getMessage());
                throw new RuntimeException("Erreur lors de la mise à jour du statut du candidat");
            }
        });
    }

    /**
     * Envoie un email d'acceptation au candidat
     */
    private void sendAcceptanceEmail(String candidateEmail, String candidateName) {
        try {
            System.out.println("📧 Envoi de l'email d'acceptation à: " + candidateEmail);

            // TODO: Implémenter l'envoi d'email réel avec un service d'email
            // Pour l'instant, simulation avec des logs

            String subject = "Félicitations ! Votre candidature a été acceptée";
            String body = String.format(
                "Bonjour %s,\n\n" +
                "Nous avons le plaisir de vous informer que votre candidature a été acceptée !\n\n" +
                "Votre performance au test technique a été remarquable et nous souhaitons poursuivre le processus de recrutement avec vous.\n\n" +
                "Nous vous recontacterons très prochainement pour organiser un entretien.\n\n" +
                "Félicitations et à bientôt !\n\n" +
                "L'équipe de recrutement",
                candidateName
            );

            // Simulation d'envoi d'email
            System.out.println("📧 Email d'acceptation envoyé:");
            System.out.println("   Destinataire: " + candidateEmail);
            System.out.println("   Sujet: " + subject);
            System.out.println("   Corps: " + body);

            // TODO: Remplacer par un vrai service d'email
            // emailService.sendEmail(candidateEmail, subject, body);

        } catch (Exception e) {
            System.err.println("❌ Erreur lors de l'envoi de l'email d'acceptation: " + e.getMessage());
        }
    }

    /**
     * Envoie un email de refus au candidat
     */
    private void sendRejectionEmail(String candidateEmail, String candidateName) {
        try {
            System.out.println("📧 Envoi de l'email de refus à: " + candidateEmail);

            String subject = "Mise à jour concernant votre candidature";
            String body = String.format(
                "Bonjour %s,\n\n" +
                "Nous vous remercions pour l'intérêt que vous avez porté à notre entreprise et pour le temps que vous avez consacré à passer notre test technique.\n\n" +
                "Après avoir examiné attentivement votre candidature, nous regrettons de vous informer que nous ne pourrons pas donner suite à votre candidature pour ce poste.\n\n" +
                "Cette décision ne remet pas en question vos compétences, mais reflète simplement l'adéquation avec les besoins spécifiques de ce poste.\n\n" +
                "Nous vous encourageons à postuler pour d'autres opportunités qui pourraient mieux correspondre à votre profil.\n\n" +
                "Nous vous souhaitons beaucoup de succès dans vos recherches.\n\n" +
                "Cordialement,\n" +
                "L'équipe de recrutement",
                candidateName
            );

            // Simulation d'envoi d'email
            System.out.println("📧 Email de refus envoyé:");
            System.out.println("   Destinataire: " + candidateEmail);
            System.out.println("   Sujet: " + subject);
            System.out.println("   Corps: " + body);

            // TODO: Remplacer par un vrai service d'email
            // emailService.sendEmail(candidateEmail, subject, body);

        } catch (Exception e) {
            System.err.println("❌ Erreur lors de l'envoi de l'email de refus: " + e.getMessage());
        }
    }

    /**
     * Récupère tous les résultats de test avec les scores calculés et les informations du profil
     */
    @GetMapping("/results")
    public Mono<List<Map<String, Object>>> getAllTestResults() {
        return Mono.fromCallable(() -> {
            try {
                List<TestResultService.TestResultWithScore> results = testResultService.getAllTestResultsWithScores();
                List<Map<String, Object>> enrichedResults = new ArrayList<>();

                for (TestResultService.TestResultWithScore result : results) {
                    Map<String, Object> enrichedResult = new HashMap<>();

                    // Copier les données du résultat de test
                    enrichedResult.put("id", result.getId());
                    enrichedResult.put("testId", result.getTestId());
                    enrichedResult.put("testName", result.getTestName());
                    enrichedResult.put("candidateName", result.getCandidateName());
                    enrichedResult.put("submittedAt", result.getSubmittedAt());
                    enrichedResult.put("totalQuestions", result.getTotalQuestions());
                    enrichedResult.put("correctAnswers", result.getCorrectAnswers());
                    enrichedResult.put("scorePercentage", result.getScorePercentage());
                    enrichedResult.put("status", result.getStatus());
                    enrichedResult.put("userId", result.getUserId());

                    // Enrichir avec les informations du profil utilisateur
                    if (result.getUserId() != null) {
                        try {
                            UserProfile profile = userProfileService.getProfileByUserId(result.getUserId());
                            if (profile != null) {
                                enrichedResult.put("candidateFirstName", profile.getFirstName());
                                enrichedResult.put("candidateLastName", profile.getLastName());
                                enrichedResult.put("candidateEmail", profile.getEmail());
                                enrichedResult.put("candidatePhotoUrl", profile.getPhotoUrl());
                                enrichedResult.put("candidatePhoneNumber", profile.getPhoneNumber());

                                // Mettre à jour le nom complet si disponible
                                if (profile.getFirstName() != null && profile.getLastName() != null) {
                                    enrichedResult.put("candidateName", profile.getFirstName() + " " + profile.getLastName());
                                }
                            }
                        } catch (Exception e) {
                            System.err.println("⚠️ Impossible de récupérer le profil pour l'utilisateur: " + result.getUserId());
                        }
                    }

                    enrichedResults.add(enrichedResult);
                }

                System.out.println("✅ " + enrichedResults.size() + " résultats de test récupérés avec informations de profil");
                return enrichedResults;

            } catch (Exception e) {
                System.err.println("❌ Erreur lors de la récupération des résultats enrichis: " + e.getMessage());
                throw new RuntimeException("Erreur lors de la récupération des résultats de test");
            }
        });
    }

    /**
     * Récupère un résultat de test spécifique avec son score
     */
    @GetMapping("/results/{resultId}")
    public Mono<TestResultService.TestResultWithScore> getTestResult(@PathVariable String resultId) {
        return Mono.fromCallable(() -> testResultService.getTestResultWithScore(resultId))
                .switchIfEmpty(Mono.error(new RuntimeException("Test result not found with id: " + resultId)));
    }
    /**
     * Supprime un résultat de test spécifique
     */
    @DeleteMapping("/results/{resultId}")
    public Mono<Map<String, String>> deleteTestResult(@PathVariable String resultId) {
        return Mono.fromCallable(() -> {
            try {
                // Vérifier si le résultat existe
                Optional<TestResult> testResultOpt = testResultRepository.findById(resultId);
                if (!testResultOpt.isPresent()) {
                    throw new RuntimeException("Résultat de test non trouvé avec l'ID: " + resultId);
                }

                // Supprimer le résultat
                testResultRepository.deleteById(resultId);

                Map<String, String> response = new HashMap<>();
                response.put("message", "Résultat de test supprimé avec succès");
                response.put("deletedId", resultId);

                System.out.println("✅ Résultat de test supprimé: " + resultId);
                return response;

            } catch (Exception e) {
                System.err.println("❌ Erreur lors de la suppression du résultat: " + e.getMessage());
                throw new RuntimeException("Erreur lors de la suppression du résultat de test");
            }
        });
    }

    /**
     * Construit un prompt personnalisé pour la génération de test selon les paramètres
     */
    private String buildCustomPrompt(String skill, String level, Integer questionCount, String testType, Integer duration) {
        String difficultyText = getDifficultyText(level);
        String typeText = getTestTypeText(testType);
        String questionTypeInstruction = getQuestionTypeInstruction(testType);

        return String.format("""
                Génère un test technique personnalisé avec les spécifications suivantes:

                PARAMÈTRES DU TEST:
                - Sujet/Compétence: %s
                - Niveau de difficulté: %s (%s)
                - Nombre de questions: %d
                - Type de test: %s (%s)
                - Durée estimée: %d minutes

                INSTRUCTIONS SPÉCIFIQUES:
                %s

                CONTRAINTES:
                - Les questions doivent être adaptées au niveau %s
                - Le test doit pouvoir être complété en %d minutes environ
                - Chaque question doit avoir minimum 4 options de réponse
                - Les questions doivent être variées et progressives en difficulté

                FORMAT DE RÉPONSE REQUIS (JSON strict):
                {
                  "title": "Test %s - Niveau %s",
                  "questions": [
                    {
                      "content": "Votre question ici...",
                      "type": "RADIO",
                      "options": ["Option 1", "Option 2", "Option 3", "Option 4"],
                      "correctOptions": [0]
                    }
                  ]
                }

                RÈGLES IMPORTANTES:
                - type: "RADIO" pour sélection simple, "SELECT" pour sélection multiple
                - correctOptions: tableau des indices des bonnes réponses (commence à 0)
                - Assure-toi que le JSON est valide et bien formaté
                - Génère exactement %d questions
                """,
                skill, level, difficultyText, questionCount, testType, typeText,
                questionTypeInstruction, level, duration, skill, level, questionCount);
    }

    private String getDifficultyText(String level) {
        return switch (level.toLowerCase()) {
            case "debutant" -> "Questions de base, concepts fondamentaux";
            case "intermediaire" -> "Concepts avancés, applications pratiques";
            case "expert" -> "Défis complexes, expertise approfondie";
            default -> "Niveau intermédiaire";
        };
    }

    private String getTestTypeText(String testType) {
        return switch (testType.toLowerCase()) {
            case "qcm" -> "Questions à choix multiples uniquement";
            case "code" -> "Exercices de programmation et logique";
            case "mixte" -> "Combinaison de QCM et d'exercices pratiques";
            default -> "Questions variées";
        };
    }

    private String getQuestionTypeInstruction(String testType) {
        return switch (testType.toLowerCase()) {
            case "qcm" -> "Génère uniquement des questions à choix multiples (QCM) avec type 'RADIO' ou 'SELECT'";
            case "code" -> "Génère des questions de programmation, logique et résolution de problèmes avec type 'RADIO'";
            case "mixte" -> "Alterne entre questions QCM (type 'RADIO'/'SELECT') et questions de programmation";
            default -> "Génère des questions variées adaptées au contexte";
        };
    }

}